package middleware

import (
	"context"
	"net/http"
	"prj/model"
	"time"

	"golang.org/x/time/rate"
)

func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip auth check for these paths
		if r.URL.Path == "/" || r.URL.Path == "/index.html" ||
			r.URL.Path == "/login" || r.URL.Path == "/signup" ||
			r.URL.Path == "/signup.html" || r.URL.Path == "/check-email" ||
			r.URL.Path == "/profile.html" {

			cookie, err := r.<PERSON>ie("access_token")
			if err == nil && !model.IsTokenBlacklisted(cookie.Value) {
				if _, err := model.ValidateToken(cookie.Value); err == nil {
					http.Redirect(w, r, "/home.html", http.StatusTemporaryRedirect)
					return
				}
			}
			next.ServeHTTP(w, r)
			return
		}

		// Skip auth for static files
		if r.URL.Path == "/css/" || r.URL.Path == "/js/" ||
			r.URL.Path == "/favicon.ico" {
			next.ServeHTTP(w, r)
			return
		}

		cookie, err := r.<PERSON>ie("access_token")
		if err != nil {
			if _, err := r.Cookie("refresh_token"); err == nil {
				http.Redirect(w, r, "/refresh", http.StatusTemporaryRedirect)
				return
			}

			http.Redirect(w, r, "/index.html", http.StatusUnauthorized)
			return
		}

		tokenString := cookie.Value
		if model.IsTokenBlacklisted(tokenString) {
			http.Redirect(w, r, "/index.html", http.StatusUnauthorized)
			return
		}

		claims, err := model.ValidateToken(tokenString)
		if err != nil {
			http.Redirect(w, r, "/index.html", http.StatusUnauthorized)
			return
		}

		ctx := context.WithValue(r.Context(), "user_email", claims.Email)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RateLimitMiddleware(next http.Handler) http.Handler {
	limiter := rate.NewLimiter(rate.Every(time.Minute), 100)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !limiter.Allow() {
			http.Error(w, "Too Many Requests", http.StatusTooManyRequests)
			return
		}
		next.ServeHTTP(w, r)
	})
}

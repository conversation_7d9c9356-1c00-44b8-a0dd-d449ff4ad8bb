<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home - MyApp</title>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/home.css">
    <link rel="stylesheet" href="css/general.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/animations.css">
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body class="page-transition">
    <nav class="navbar">
        <a href="home.html" class="navbar-logo">MyApp</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="home.html" class="nav-link active">Home</a></li>
            <li class="nav-item"><a href="users.html" class="nav-link">Users</a></li>
        </ul>
        <div class="navbar-center">
            <div class="navbar-search">
                <i data-lucide="search" class="search-icon"></i>
                <input type="text" id="navbarSearchInput" class="search-input" placeholder="Search for people...">
                <div id="navbarSearchDropdown" class="search-dropdown">
                    <div id="navbarSearchResults"></div>
                </div>
            </div>
        </div>
        <div class="navbar-right">
            <div class="notification-menu">
                <button id="notificationBtn" class="notification-btn">
                    <i data-lucide="bell"></i>
                    <span id="notificationBadge" class="notification-badge" style="display: none;">0</span>
                </button>
                <div id="notificationDropdown" class="notification-dropdown">
                    <div class="notification-header">
                        <h3>Notifications</h3>
                        <button id="markAllReadBtn" class="mark-all-read-btn">Mark all as read</button>
                    </div>
                    <div id="notificationList" class="notification-list">
                        <!-- Notifications will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="user-menu">
                <button id="userBtn" class="user-btn">
                    <span id="userEmail"></span>
                    <i data-lucide="chevron-down"></i>
                </button>
                <div id="dropdownMenu" class="dropdown-menu">
                    <a href="profile.html" class="dropdown-link">Profile</a>
                    <a href="settings.html" class="dropdown-link">Settings</a>
                    <a href="#" id="logoutBtn" class="dropdown-link">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="content-wrapper">
            <div class="post-section">
                <div class="post-form">
                    <textarea id="postContent" placeholder="What's on your mind?"></textarea>
                    <div id="mediaPreview" class="media-preview"></div>
                    <div class="post-actions">
                        <label for="mediaInput" class="btn-media">
                            <i data-lucide="image"></i> Add Media
                        </label>
                        <input type="file" id="mediaInput" accept="image/*,video/*" style="display: none;">
                        <button id="postBtn" class="btn-primary">Post</button>
                    </div>
                </div>

                <div class="posts-container" id="postsContainer">
                    <!-- Posts will be loaded here -->
                </div>
            </div>

            <div class="sidebar">
                <div class="sidebar-card">
                    <div class="sidebar-header">
                        <h3>Recent Activity</h3>
                        <button id="refreshActivityBtn" class="refresh-btn" title="Refresh activity">
                            <i data-lucide="refresh-cw"></i>
                        </button>
                    </div>
                    <div class="activity-list" id="activityList">
                        <!-- Activity will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <p>&copy;GCIT NEWS</p>
    </footer>

    <script src="js/service-worker-cleanup.js"></script>
    <script src="js/lazy-loading.js"></script>
    <script src="js/infinite-scroll.js"></script>
    <script src="js/sticky-navbar.js"></script>
    <script src="js/home.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/navbar-search.js"></script>
    <script>
        lucide.createIcons();
    </script>
</body>

</html>
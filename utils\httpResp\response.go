package httpResp

import (
	"encoding/json"
	"errors"
	"net/http"
)

func RespondWithError(w http.ResponseWriter, code int, message string) {
	RespondWithJSON(w, code, map[string]string{"error": message})
}

func RespondWithJSON(w http.ResponseWriter, code int, payload interface{}) {
	response, _ := json.Marshal(payload)
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(code)
	w.Write(response)
}

func DecodeJSONBody(w http.ResponseWriter, r *http.Request, dst interface{}) error {
	if r.Header.Get("Content-Type") != "application/json" {
		return errors.New("Content-Type header is not application/json")
	}

	r.Body = http.MaxBytesReader(w, r.Body, 1048576) // 1MB limit
	dec := json.NewDecoder(r.Body)
	dec.DisallowUnknownFields()

	if err := dec.Decode(&dst); err != nil {
		return err
	}

	if dec.More() {
		return errors.New("request body must only contain a single JSON object")
	}

	return nil
}

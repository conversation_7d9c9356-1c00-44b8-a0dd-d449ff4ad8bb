package controller

import (
	"context"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type ActivityItem struct {
	ID            string    `json:"id"`
	Type          string    `json:"type"` // "post", "like", "comment"
	UserEmail     string    `json:"userEmail"`
	UserName      string    `json:"userName"`
	Content       string    `json:"content"`
	PostID        string    `json:"postId,omitempty"`
	CreatedAt     time.Time `json:"createdAt"`
	PostOwner     string    `json:"postOwner,omitempty"`
	PostOwnerName string    `json:"postOwnerName,omitempty"`
}

func GetRecentActivity(w http.ResponseWriter, r *http.Request) {
	// Get activities from the last 24 hours
	twentyFourHoursAgo := time.Now().Add(-24 * time.Hour)

	var activities []ActivityItem

	// Get recent posts
	postCursor, err := model.PostCollection.Find(context.TODO(), bson.M{
		"createdAt": bson.M{"$gte": twentyFourHoursAgo},
	})
	if err == nil {
		defer postCursor.Close(context.TODO())

		var posts []model.Post
		if err = postCursor.All(context.TODO(), &posts); err == nil {
			for _, post := range posts {
				content := post.Content
				if len(content) > 100 {
					content = content[:100] + "..."
				}

				activities = append(activities, ActivityItem{
					ID:            post.ID,
					Type:          "post",
					UserEmail:     post.UserEmail,
					UserName:      post.UserName,
					Content:       content,
					PostID:        post.ID,
					CreatedAt:     post.CreatedAt,
					PostOwner:     post.UserEmail, // For posts, the owner is the same as the user who posted
					PostOwnerName: post.UserName,  // Will be updated later with current profile data
				})
			}
		}
	}

	// Get recent notifications (likes and comments) from the last 24 hours
	notificationCursor, err := model.NotificationCollection.Find(context.TODO(), bson.M{
		"createdAt": bson.M{"$gte": twentyFourHoursAgo},
	})
	if err == nil {
		defer notificationCursor.Close(context.TODO())

		var notifications []model.Notification
		if err = notificationCursor.All(context.TODO(), &notifications); err == nil {
			for _, notification := range notifications {
				var content string
				var activityType string

				if notification.Type == "like" {
					content = "liked a post"
					activityType = "like"
				} else if notification.Type == "comment" {
					content = "commented: " + notification.CommentText
					if len(content) > 100 {
						content = content[:100] + "..."
					}
					activityType = "comment"
				}

				activities = append(activities, ActivityItem{
					ID:            notification.ID,
					Type:          activityType,
					UserEmail:     notification.ActorEmail,
					UserName:      notification.ActorName,
					Content:       content,
					PostID:        notification.PostID,
					CreatedAt:     notification.CreatedAt,
					PostOwner:     notification.UserEmail,
					PostOwnerName: "", // Will be updated later with current profile data
				})
			}
		}
	}

	// Sort activities by creation time (newest first)
	sort.Slice(activities, func(i, j int) bool {
		return activities[i].CreatedAt.After(activities[j].CreatedAt)
	})

	// Limit to 20 most recent activities
	if len(activities) > 20 {
		activities = activities[:20]
	}

	// Get all user profiles to update names
	userProfiles := make(map[string]model.User)
	userCursor, err := model.UserCollection.Find(context.TODO(), bson.M{})
	if err == nil {
		defer userCursor.Close(context.TODO())

		var users []model.User
		if err = userCursor.All(context.TODO(), &users); err == nil {
			for _, user := range users {
				userProfiles[user.Email] = user
			}
		}
	}

	// Update user names with current profile data
	for i := range activities {
		if user, exists := userProfiles[activities[i].UserEmail]; exists {
			activities[i].UserName = user.FirstName + " " + user.LastName
		}
		// Also update post owner names for better context
		if activities[i].PostOwner != "" {
			if owner, exists := userProfiles[activities[i].PostOwner]; exists {
				activities[i].PostOwnerName = owner.FirstName + " " + owner.LastName
			}
		}
	}

	httpResp.RespondWithJSON(w, http.StatusOK, activities)
}

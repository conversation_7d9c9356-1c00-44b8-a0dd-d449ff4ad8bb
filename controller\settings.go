package controller

import (
	"net/http"
	"prj/model"
	"prj/utils/httpResp"

	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/crypto/bcrypt"
)

func UpdateProfile(w http.ResponseWriter, r *http.Request) {
	var updateData struct {
		Firstname string `json:"firstname"`
		Lastname  string `json:"lastname"`
	}

	if err := httpResp.DecodeJSONBody(w, r, &updateData); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if updateData.Firstname == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "First name is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Update user in database
	_, err := model.UserCollection.UpdateOne(
		r.Context(),
		bson.M{"email": email},
		bson.M{"$set": bson.M{
			"firstname": updateData.Firstname,
			"lastname":  updateData.Lastname,
		}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to update profile")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "profile updated"})
}

func ChangePassword(w http.ResponseWriter, r *http.Request) {
	var passwordData struct {
		CurrentPassword string `json:"currentPassword"`
		NewPassword     string `json:"newPassword"`
	}

	if err := httpResp.DecodeJSONBody(w, r, &passwordData); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if passwordData.CurrentPassword == "" || passwordData.NewPassword == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Both current and new password are required")
		return
	}

	if !model.IsPasswordStrong(passwordData.NewPassword) {
		httpResp.RespondWithError(w, http.StatusBadRequest,
			"password must be at least 8 characters long with uppercase, lowercase, number, and special character")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Get user from database
	var user model.User
	err := model.UserCollection.FindOne(r.Context(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user")
		return
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(passwordData.CurrentPassword))
	if err != nil {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "invalid current password")
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(passwordData.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "could not hash password")
		return
	}

	// Update password in database
	_, err = model.UserCollection.UpdateOne(
		r.Context(),
		bson.M{"email": email},
		bson.M{"$set": bson.M{"password": string(hashedPassword)}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to update password")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "password updated"})
}

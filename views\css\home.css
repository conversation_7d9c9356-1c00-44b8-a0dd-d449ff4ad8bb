.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.post-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sidebar {
    position: sticky;
    top: 80px;
    height: fit-content;
}

.sidebar-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 2px solid #4a6cf7;
    padding-bottom: 0.5rem;
}

.sidebar-card h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.refresh-btn {
    background: none;
    border: none;
    color: #4a6cf7;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s, transform 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background-color: rgba(74, 108, 247, 0.1);
    transform: rotate(90deg);
}

.refresh-btn i {
    width: 16px;
    height: 16px;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #4a6cf7;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.activity-item[data-activity-type="post"] .activity-icon {
    background-color: #28a745;
}

.activity-item[data-activity-type="like"] .activity-icon {
    background-color: #dc3545;
}

.activity-item[data-activity-type="comment"] .activity-icon {
    background-color: #17a2b8;
}

.activity-item[data-activity-type="comment_like"] .activity-icon {
    background-color: #dc3545;
}

.activity-item[data-activity-type="comment_reply"] .activity-icon {
    background-color: #28a745;
}

.activity-item[data-activity-type="reply_like"] .activity-icon {
    background-color: #dc3545;
}

.activity-icon i {
    width: 16px;
    height: 16px;
    color: white;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-text {
    font-size: 0.9rem;
    color: #333;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.activity-text strong {
    font-weight: 600;
    color: #4a6cf7;
}

.activity-item[data-is-current-user="true"] .activity-text strong {
    color: #28a745;
    font-weight: 700;
}

.activity-item[data-is-current-user="true"] {
    background-color: rgba(40, 167, 69, 0.05);
    border-left: 3px solid #28a745;
}

.activity-preview {
    font-size: 0.85rem;
    color: #555;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #4a6cf7;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.4;
}

.activity-time {
    font-size: 0.75rem;
    color: #888;
}

.no-activity {
    padding: 1rem;
    text-align: center;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.activity-loading {
    padding: 1rem;
    text-align: center;
    color: #4a6cf7;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.activity-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #4a6cf7;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-posts {
    text-align: center;
    color: #666;
    padding: 2rem;
    font-style: italic;
}

.post-form {
    margin-bottom: 2rem;
}

.post-form textarea {
    width: 100%;
    min-height: 100px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.post-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.media-preview {
    position: relative;
    margin-bottom: 1rem;
    display: none;
}

.media-preview img,
.media-preview video {
    max-height: 400px;
    border-radius: 4px;
}

.remove-media-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
}

.remove-media-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

@media (max-width: 768px) {
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        position: static;
    }
}
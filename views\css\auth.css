* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html {
    overflow: hidden;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    overflow: hidden;
    width: 100%;
    height: 100vh;
    position: relative;
}

/* Auth Container */
.auth-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.1),
        0 10px 10px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 380px;
    max-width: 100%;
    text-align: center;
    position: relative;
}

/* Add this to auth.css */
.auth-container.disabled {
    pointer-events: none;
    opacity: 0.7;
}

.auth-container h2 {
    margin-bottom: 30px;
    color: #333;
    font-weight: 600;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
    text-align: left;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #4a6cf7;
}

.form-group input.error {
    border-color: #ff4444 !important;
}

/* Password input container */
.password-input-container {
    position: relative;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.toggle-password:hover {
    color: #4a6cf7;
}

.toggle-password i {
    width: 18px;
    height: 18px;
}

/* Button Styles */
button[type="submit"] {
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 20px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 10px;
    position: relative;
}

button[type="submit"]:hover {
    background-color: #3a5bd9;
}

button[type="submit"]:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
}

/* Enhanced loading state for perfect centering */
button[type="submit"].btn-loading {
    overflow: hidden;
}

/* Link Styles */
.auth-container p {
    margin-top: 20px;
    color: #666;
    font-size: 14px;
}

.auth-container a {
    color: #4a6cf7;
    text-decoration: none;
    font-weight: 500;
}

.auth-container a:hover {
    text-decoration: underline;
}

/* Alert Messages */
.alert-error {
    background-color: #ff4444;
    color: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    transition: opacity 0.5s;
    animation: fadeIn 0.3s ease-in;
}

.alert-success {
    background-color: #00C851;
    color: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    transition: opacity 0.5s;
    animation: fadeIn 0.3s ease-in;
}

/* Field Error Messages */
.field-error {
    color: #ff4444;
    font-size: 12px;
    margin-top: 5px;
    animation: fadeIn 0.3s ease-in;
}

/* Spinner Styles */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
}

/* Animations */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        top: -50px;
        opacity: 0;
    }

    to {
        top: 20px;
        opacity: 1;
    }
}

/* Password strength meter */
.password-strength {
    height: 5px;
    margin-top: 5px;
    background-color: #eee;
    border-radius: 3px;
    overflow: hidden;
}

.password-strength-meter {
    height: 100%;
    width: 0;
    transition: width 0.3s, background-color 0.3s;
}

/* Password requirements */
.password-requirements {
    margin-top: 8px;
}

.password-requirements ul {
    list-style: none;
    padding-left: 5px;
    margin-top: 5px;
}

.password-requirements li {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
}

.req-icon {
    display: inline-block;
    width: 16px;
    text-align: center;
    margin-right: 5px;
    font-size: 10px;
}

.password-requirements li.valid {
    color: #00C851;
}

.password-requirements li.valid .req-icon {
    content: "✓";
    color: #00C851;
}


/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        padding: 20px;
        width: 90%;
    }

    .auth-container h2 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .form-group input {
        padding: 10px 12px;
    }

    button[type="submit"] {
        padding: 10px 15px;
        font-size: 15px;
    }
}
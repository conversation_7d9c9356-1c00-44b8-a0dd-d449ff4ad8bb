
// Navbar Search Functionality
class NavbarSearch {
    constructor() {
        this.searchInput = document.getElementById('navbarSearchInput');
        this.searchDropdown = document.getElementById('navbarSearchDropdown');
        this.searchResults = document.getElementById('navbarSearchResults');
        this.users = [];
        this.searchTimeout = null;
        this.searchHistory = this.loadSearchHistory();
        this.searchCache = new Map(); // Cache search results

        // Clear cache on initialization to ensure fresh results
        this.searchCache.clear();

        this.init();
    }

    init() {
        if (!this.searchInput || !this.searchDropdown || !this.searchResults) {
            console.warn('Navbar search elements not found');
            return;
        }

        this.loadUsers();
        this.bindEvents();
    }

    async loadUsers() {
        try {
            const res = await fetch('/api/users', {
                credentials: 'include'
            });

            if (res.ok) {
                this.users = await res.json();
                console.log('Loaded users for search:', this.users.length);
            }
        } catch (err) {
            console.error('Error loading users for search:', err);
        }
    }

    bindEvents() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            // Reduced timeout for faster response with single character searches
            this.searchTimeout = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 200);
        });

        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.trim()) {
                this.handleSearch(this.searchInput.value);
            } else {
                // Show recent searches when focused with empty input
                this.showRecentSearches();
            }
        });

        this.searchInput.addEventListener('blur', () => {
            // Delay hiding to allow clicking on results
            setTimeout(() => {
                this.hideDropdown();
            }, 200);
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.searchDropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });

        // Handle keyboard navigation
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideDropdown();
                this.searchInput.blur();
            }
        });
    }

    async handleSearch(query) {
        const trimmedQuery = query.trim();

        if (trimmedQuery.length === 0) {
            this.showRecentSearches();
            return;
        }

        // Now works with minimum 1 character
        if (trimmedQuery.length < 1) {
            return;
        }

        // Check cache first
        if (this.searchCache.has(trimmedQuery.toLowerCase())) {
            const cachedResults = this.searchCache.get(trimmedQuery.toLowerCase());
            this.renderResults(cachedResults, trimmedQuery, false);
            this.showDropdown();
            return;
        }

        try {
            // Use the search API endpoint for better performance
            const res = await fetch(`/api/users/search?q=${encodeURIComponent(trimmedQuery)}`, {
                credentials: 'include'
            });

            if (res.ok) {
                const users = await res.json();
                // Cache the results
                this.searchCache.set(trimmedQuery.toLowerCase(), users);
                this.renderResults(users, trimmedQuery, false);
                this.showDropdown();
            } else {
                // Fallback to client-side search if API fails
                this.clientSideSearch(trimmedQuery);
            }
        } catch (err) {
            console.error('Search API error, falling back to client-side search:', err);
            this.clientSideSearch(trimmedQuery);
        }
    }

    clientSideSearch(trimmedQuery) {
        const filteredUsers = this.users.filter(user => {
            const fullName = `${user.firstname} ${user.lastname}`.toLowerCase();
            const queryLower = trimmedQuery.toLowerCase();

            return fullName.startsWith(queryLower) ||
                   user.firstname.toLowerCase().startsWith(queryLower) ||
                   user.lastname.toLowerCase().startsWith(queryLower);
        });

        // Cache the results
        this.searchCache.set(trimmedQuery.toLowerCase(), filteredUsers);
        this.renderResults(filteredUsers, trimmedQuery, false);
        this.showDropdown();
    }

    renderResults(users, query, isRecentSearch = false) {
        this.searchResults.innerHTML = '';

        if (!isRecentSearch && users.length === 0) {
            this.searchResults.innerHTML = `
                <div class="no-search-results">
                    No users found for "${query}"
                </div>
            `;
            return;
        }

        // Limit to 8 results for better UX
        const limitedUsers = users.slice(0, 8);

        limitedUsers.forEach(user => {
            const resultElement = document.createElement('div');
            resultElement.className = 'search-result';

            const initials = `${user.firstname.charAt(0)}${user.lastname.charAt(0)}`.toUpperCase();

            resultElement.innerHTML = `
                <div class="search-result-avatar">${initials}</div>
                <div class="search-result-info">
                    <div class="search-result-name">${user.firstname} ${user.lastname}</div>
                </div>
                ${isRecentSearch ? `<div class="remove-search" data-email="${user.email}">
                    <i data-lucide="x" style="width: 14px; height: 14px; color: #666;"></i>
                </div>` : ''}
            `;

            resultElement.addEventListener('click', (e) => {
                // Don't navigate if clicking the remove button
                if (e.target.closest('.remove-search')) {
                    e.stopPropagation();
                    this.removeFromHistory(user.email);
                    return;
                }
                // For recent searches, don't pass query to avoid re-adding to history
                this.navigateToUser(user.email, isRecentSearch ? null : query);
            });

            this.searchResults.appendChild(resultElement);
        });

        // Add "View all results" if there are more users and it's not recent search
        if (!isRecentSearch && users.length > 8) {
            const viewAllElement = document.createElement('div');
            viewAllElement.className = 'search-result';
            viewAllElement.style.borderTop = '1px solid #e0e0e0';
            viewAllElement.style.fontWeight = '600';
            viewAllElement.style.color = '#4a6cf7';

            viewAllElement.innerHTML = `
                <div class="search-result-avatar">
                    <i data-lucide="users" style="width: 16px; height: 16px;"></i>
                </div>
                <div class="search-result-info">
                    <div class="search-result-name">View all ${users.length} results</div>
                </div>
            `;

            viewAllElement.addEventListener('click', () => {
                this.navigateToUsersPage(query);
            });

            this.searchResults.appendChild(viewAllElement);
        }

        // Add clear history option for recent searches
        if (isRecentSearch && users.length > 0) {
            const clearAllElement = document.createElement('div');
            clearAllElement.className = 'search-result';
            clearAllElement.style.borderTop = '1px solid #e0e0e0';
            clearAllElement.style.fontWeight = '600';
            clearAllElement.style.color = '#666';
            clearAllElement.style.textAlign = 'center';

            clearAllElement.innerHTML = `
                <div class="search-result-info" style="text-align: center;">
                    <div class="search-result-name">Clear search history</div>
                </div>
            `;

            clearAllElement.addEventListener('click', () => {
                this.clearSearchHistory();
            });

            this.searchResults.appendChild(clearAllElement);
        }

        // Re-create icons for new elements
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    navigateToUser(email, searchQuery = null) {
        // Add to search history if it was from a search
        if (searchQuery) {
            this.addToSearchHistory(email);
        }

        this.hideDropdown();
        this.searchInput.value = '';
        window.location.href = `user-profile.html?email=${encodeURIComponent(email)}`;
    }

    navigateToUsersPage(query) {
        this.hideDropdown();
        this.searchInput.value = '';
        window.location.href = `users.html?search=${encodeURIComponent(query)}`;
    }

    showDropdown() {
        this.searchDropdown.classList.add('show');
    }

    hideDropdown() {
        this.searchDropdown.classList.remove('show');
    }

    // Search History Management
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('navbarSearchHistory');
            return history ? JSON.parse(history) : [];
        } catch (err) {
            console.error('Error loading search history:', err);
            return [];
        }
    }

    saveSearchHistory() {
        try {
            localStorage.setItem('navbarSearchHistory', JSON.stringify(this.searchHistory));
        } catch (err) {
            console.error('Error saving search history:', err);
        }
    }

    addToSearchHistory(email) {
        // Find the user in our users array
        const user = this.users.find(u => u.email === email);
        if (!user) return;

        // Remove if already exists (to move to top)
        this.searchHistory = this.searchHistory.filter(item => item.email !== email);
        
        // Add to beginning of array
        this.searchHistory.unshift({
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            timestamp: Date.now()
        });

        // Keep only last 10 searches
        this.searchHistory = this.searchHistory.slice(0, 10);
        
        this.saveSearchHistory();
    }

    removeFromHistory(email) {
        this.searchHistory = this.searchHistory.filter(item => item.email !== email);
        this.saveSearchHistory();

        // Refresh the display
        if (this.searchInput.value.trim() === '') {
            this.showRecentSearches();
        }
    }

    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.hideDropdown();
    }

    showRecentSearches() {
        if (this.searchHistory.length === 0) {
            this.hideDropdown();
            return;
        }

        // Show recent searches with a header
        this.searchResults.innerHTML = `
            <div class="search-section-header">Recent searches</div>
        `;

        this.renderResults(this.searchHistory, '', true);
        this.showDropdown();
    }

    // Method to refresh users data (can be called from other scripts)
    refreshUsers() {
        this.loadUsers();
    }
}

// Initialize navbar search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure other scripts have loaded
    setTimeout(() => {
        window.navbarSearch = new NavbarSearch();
    }, 100);
});


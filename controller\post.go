package controller

import (
	"context"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"prj/model"
	"prj/utils/httpResp"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreatePost(w http.ResponseWriter, r *http.Request) {
	err := r.ParseMultipartForm(32 << 20)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Failed to parse form")
		return
	}

	content := r.FormValue("content")
	if content == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Content is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	var user model.User
	err = model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user details")
		return
	}

	var mediaURL, mediaType string
	file, handler, err := r.FormFile("media")
	if err == nil {
		defer file.Close()

		contentType := handler.Header.Get("Content-Type")
		if !strings.HasPrefix(contentType, "image/") && !strings.HasPrefix(contentType, "video/") {
			httpResp.RespondWithError(w, http.StatusBadRequest, "Only images and videos are allowed")
			return
		}

		if _, err := os.Stat("uploads"); os.IsNotExist(err) {
			os.Mkdir("uploads", 0755)
		}

		ext := filepath.Ext(handler.Filename)
		filename := primitive.NewObjectID().Hex() + ext
		filePath := filepath.Join("uploads", filename)

		dst, err := os.Create(filePath)
		if err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to save file")
			return
		}
		defer dst.Close()

		if _, err := io.Copy(dst, file); err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to save file")
			return
		}

		mediaURL = "/uploads/" + filename
		if strings.HasPrefix(contentType, "image/") {
			mediaType = "image"
		} else {
			mediaType = "video"
		}
	}

	post := model.Post{
		ID:        primitive.NewObjectID().Hex(),
		Content:   content,
		UserEmail: email,
		UserName:  user.FirstName + " " + user.LastName,
		CreatedAt: time.Now(),
		Likes:     0,
		LikedBy:   []string{},
		MediaURL:  mediaURL,
		MediaType: mediaType,
	}

	_, err = model.PostCollection.InsertOne(context.TODO(), post)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to create post")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusCreated, post)
}

func GetPosts(w http.ResponseWriter, r *http.Request) {
	cursor, err := model.PostCollection.Find(context.TODO(), bson.M{})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch posts")
		return
	}
	defer cursor.Close(context.TODO())

	var posts []model.Post
	if err = cursor.All(context.TODO(), &posts); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to decode posts")
		return
	}

	// Ensure LikedBy is never nil for frontend
	for i := range posts {
		if posts[i].LikedBy == nil {
			posts[i].LikedBy = []string{}
		}
	}

	httpResp.RespondWithJSON(w, http.StatusOK, posts)
}

func UpdatePost(w http.ResponseWriter, r *http.Request) {
	postID := r.URL.Query().Get("post_id")
	if postID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	var updateData struct {
		Content string `json:"content"`
	}

	if err := httpResp.DecodeJSONBody(w, r, &updateData); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if updateData.Content == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Content cannot be empty")
		return
	}

	// Verify the post belongs to the user
	var existingPost model.Post
	err := model.PostCollection.FindOne(r.Context(), bson.M{"_id": postID}).Decode(&existingPost)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
		return
	}

	if existingPost.UserEmail != email {
		httpResp.RespondWithError(w, http.StatusForbidden, "You can only update your own posts")
		return
	}

	// Update the post
	_, err = model.PostCollection.UpdateOne(
		r.Context(),
		bson.M{"_id": postID},
		bson.M{"$set": bson.M{"content": updateData.Content}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to update post")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "post updated"})
}

func DeletePost(w http.ResponseWriter, r *http.Request) {
	postID := r.URL.Query().Get("post_id")
	if postID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Verify the post belongs to the user
	var existingPost model.Post
	err := model.PostCollection.FindOne(r.Context(), bson.M{"_id": postID}).Decode(&existingPost)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
		return
	}

	if existingPost.UserEmail != email {
		httpResp.RespondWithError(w, http.StatusForbidden, "You can only delete your own posts")
		return
	}

	// Delete the post
	_, err = model.PostCollection.DeleteOne(r.Context(), bson.M{"_id": postID})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to delete post")
		return
	}

	// If there's media associated with the post, delete it
	if existingPost.MediaURL != "" {
		filePath := strings.TrimPrefix(existingPost.MediaURL, "/")
		if _, err := os.Stat(filePath); err == nil {
			os.Remove(filePath)
		}
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "post deleted"})
}

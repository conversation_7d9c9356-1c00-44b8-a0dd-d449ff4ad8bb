<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - MyApp</title>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/users.css">
    <link rel="stylesheet" href="css/general.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/animations.css">
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body>
    <nav class="navbar">
        <a href="home.html" class="navbar-logo">MyApp</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="home.html" class="nav-link">Home</a></li>
            <li class="nav-item"><a href="users.html" class="nav-link active">Users</a></li>
        </ul>
        <div class="navbar-center">
            <div class="navbar-search">
                <i data-lucide="search" class="search-icon"></i>
                <input type="text" id="navbarSearchInput" class="search-input" placeholder="Search for people...">
                <div id="navbarSearchDropdown" class="search-dropdown">
                    <div id="navbarSearchResults"></div>
                </div>
            </div>
        </div>
        <div class="user-menu">
            <button id="userBtn" class="user-btn">
                <span id="userEmail"></span>
                <i data-lucide="chevron-down"></i>
            </button>
            <div id="dropdownMenu" class="dropdown-menu">
                <a href="profile.html" class="dropdown-link">Profile</a>
                <a href="settings.html" class="dropdown-link">Settings</a>
                <a href="#" id="logoutBtn" class="dropdown-link">Logout</a>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="content-wrapper">
            <h1>All Users</h1>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search users...">
            </div>
            <div class="users-container" id="usersContainer">
                <!-- Users will be loaded here -->
            </div>
        </div>
    </main>

    <footer class="footer">
        <p>&copy; GCIT NEWS</p>
    </footer>

    <script src="js/lazy-loading.js"></script>
    <script src="js/infinite-scroll.js"></script>
    <script src="js/users.js"></script>
    <script src="js/navbar-search.js"></script>
    <script>
        lucide.createIcons();
    </script>
</body>

</html>
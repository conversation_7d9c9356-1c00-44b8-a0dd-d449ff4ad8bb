package controller

import (
	"context"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func CreateNotification(userEmail, actorEmail, actorName, notificationType, postID, postContent, commentText string) error {
	// Don't create notification if user is acting on their own post
	if userEmail == actorEmail {
		return nil
	}

	notification := model.Notification{
		ID:          primitive.NewObjectID().Hex(),
		UserEmail:   userEmail,
		ActorEmail:  actorEmail,
		ActorName:   actorName,
		Type:        notificationType,
		PostID:      postID,
		PostContent: postContent,
		CommentText: commentText,
		IsRead:      false,
		CreatedAt:   time.Now(),
	}

	_, err := model.NotificationCollection.InsertOne(context.TODO(), notification)
	return err
}

func GetNotifications(w http.ResponseWriter, r *http.Request) {
	email := r.Context().Value("user_email").(string)

	// Get notifications for the user, sorted by newest first
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}}).SetLimit(20)
	cursor, err := model.NotificationCollection.Find(context.TODO(), bson.M{"userEmail": email}, opts)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch notifications")
		return
	}
	defer cursor.Close(context.TODO())

	var notifications []model.Notification
	if err = cursor.All(context.TODO(), &notifications); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to decode notifications")
		return
	}

	// Ensure notifications is never nil
	if notifications == nil {
		notifications = []model.Notification{}
	}

	httpResp.RespondWithJSON(w, http.StatusOK, notifications)
}

func MarkNotificationAsRead(w http.ResponseWriter, r *http.Request) {
	notificationID := r.URL.Query().Get("notification_id")
	if notificationID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "notification_id is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Update notification as read
	_, err := model.NotificationCollection.UpdateOne(
		context.TODO(),
		bson.M{"_id": notificationID, "userEmail": email},
		bson.M{"$set": bson.M{"isRead": true}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to mark notification as read")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "notification marked as read"})
}

func MarkAllNotificationsAsRead(w http.ResponseWriter, r *http.Request) {
	email := r.Context().Value("user_email").(string)

	// Update all notifications as read
	_, err := model.NotificationCollection.UpdateMany(
		context.TODO(),
		bson.M{"userEmail": email, "isRead": false},
		bson.M{"$set": bson.M{"isRead": true}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to mark notifications as read")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "all notifications marked as read"})
}

func GetUnreadNotificationCount(w http.ResponseWriter, r *http.Request) {
	email := r.Context().Value("user_email").(string)

	count, err := model.NotificationCollection.CountDocuments(
		context.TODO(),
		bson.M{"userEmail": email, "isRead": false},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to count notifications")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]int64{"count": count})
}

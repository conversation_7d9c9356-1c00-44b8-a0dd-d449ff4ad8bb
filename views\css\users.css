/* users.css */
.content-wrapper {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.content-wrapper h1 {
    margin-bottom: 1.5rem;
    color: #333;
}

.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-container input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.search-container i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.users-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.user-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    float: left;
}

.user-avatar i {
    width: 24px;
    height: 24px;
    color: #666;
}

.user-info {
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 0.8rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.no-users {
    text-align: center;
    color: #666;
    padding: 2rem;
    grid-column: 1 / -1;
}

@media (max-width: 768px) {
    .users-container {
        grid-template-columns: 1fr;
    }
}
document.addEventListener('DOMContentLoaded', function () {
    lucide.createIcons();

    const userBtn = document.getElementById('userBtn');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const logoutBtn = document.getElementById('logoutBtn');
    const userEmailSpan = document.getElementById('userEmail');
    const profileName = document.getElementById('profileName');
    const profileEmail = document.getElementById('profileEmail');
    const postCount = document.getElementById('postCount');
    const postsContainer = document.getElementById('postsContainer');

    // Toggle dropdown
    userBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
    dropdownMenu.addEventListener('click', (e) => e.stopPropagation());

    logoutBtn.addEventListener('click', async function (e) {
        e.preventDefault();
        try {
            const res = await fetch('/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                // Use replace to avoid back button issues and full URL to bypass service worker
                window.location.replace(window.location.origin + '/index.html');
            } else {
                throw new Error('Logout failed');
            }
        } catch (err) {
            alert('Logout failed. Try again.');
        }
    });

    checkAuthStatus();
    updateUserProfile();
    loadUserProfile();

    async function checkAuthStatus() {
        try {
            const res = await fetch('/api/check-auth', {
                method: 'GET',
                credentials: 'include'
            });

            if (!res.ok) return window.location.href = 'index.html';

            const data = await res.json();
            userEmailSpan.textContent = data.email;
        } catch (err) {
            console.error('Auth check error:', err);
            window.location.href = 'index.html';
        }
    }

    // Update user profile display in navbar
    async function updateUserProfile() {
        try {
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/profile?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const data = await res.json();
                const user = data.user;
                const fullName = `${user.firstname} ${user.lastname}`;
                userEmailSpan.textContent = fullName;
                console.log('Updated navbar user display:', fullName);
            } else {
                // Fallback to email if profile fetch fails
                const authRes = await fetch('/api/check-auth', {
                    credentials: 'include'
                });
                if (authRes.ok) {
                    const authData = await authRes.json();
                    userEmailSpan.textContent = authData.email;
                }
            }
        } catch (err) {
            console.error('Error updating user profile display:', err);
            // Fallback to email on error
            try {
                const authRes = await fetch('/api/check-auth', {
                    credentials: 'include'
                });
                if (authRes.ok) {
                    const authData = await authRes.json();
                    userEmailSpan.textContent = authData.email;
                }
            } catch (authErr) {
                console.error('Error fetching auth data:', authErr);
            }
        }
    }

    async function loadUserProfile() {
        const urlParams = new URLSearchParams(window.location.search);
        const email = urlParams.get('email');
        
        if (!email) {
            window.location.href = 'users.html';
            return;
        }

        try {
            const res = await fetch(`/api/users/${encodeURIComponent(email)}/posts`, {
                credentials: 'include'
            });

            if (!res.ok) throw new Error('Failed to load profile');

            const data = await res.json();
            renderProfile(data);
        } catch (err) {
            console.error('Error loading profile:', err);
            postsContainer.innerHTML = '<p class="no-posts">Failed to load profile. Please try again.</p>';
        }
    }

    async function renderProfile(data) {
    const { user, posts } = data;

    // Get current user's email for like status check
    let userEmail = '';
    try {
        const res = await fetch('/api/check-auth', {
            credentials: 'include'
        });
        if (res.ok) {
            const authData = await res.json();
            userEmail = authData.email;
        }
    } catch (err) {
        console.error('Error fetching user email:', err);
    }

    // Set user info
    profileName.textContent = `${user.firstname} ${user.lastname}`;
    profileEmail.textContent = user.email;
    postCount.textContent = `${posts.length} ${posts.length === 1 ? 'post' : 'posts'}`;

    // Render posts
    if (posts.length === 0) {
        postsContainer.innerHTML = '<p class="no-posts">This user hasn\'t posted anything yet.</p>';
        return;
    }

    postsContainer.innerHTML = '';

    posts.forEach(post => {
        // Check if current user liked this post
        const isLiked = post.likedBy && post.likedBy.includes(userEmail);
        const likeClass = isLiked ? 'liked' : '';
        
        let mediaContent = '';
        if (post.mediaUrl) {
            if (post.mediaType === 'image') {
                mediaContent = `<div class="post-media"><img src="${post.mediaUrl}" alt="Post image"></div>`;
            } else if (post.mediaType === 'video') {
                mediaContent = `
                    <div class="post-media">
                        <video controls>
                            <source src="${post.mediaUrl}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                `;
            }
        }

        const postElement = document.createElement('div');
        postElement.className = 'post';
        postElement.innerHTML = `
            <div class="post-header">
                <div class="post-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="post-user">${user.firstname} ${user.lastname}</span>
                    <span class="post-time">${formatDate(post.createdAt)}</span>
                </div>
            </div>
            <div class="post-content">${post.content}</div>
            ${mediaContent}
            <div class="post-actions">
                <button class="btn-like ${likeClass}" data-post-id="${post.id}">
                    <i data-lucide="thumbs-up"></i>
                    <span class="like-count">${post.likes || 0}</span>
                </button>
                <button class="btn-view-comments" data-post-id="${post.id}">
                    <i data-lucide="message-circle"></i>
                    <span class="comment-count">${post.comments ? post.comments.length : 0}</span> Comments
                </button>
            </div>
            <div class="post-comments" id="comments-${post.id}" style="display: none;">
                <div class="comments-container" id="comments-container-${post.id}">
                    <!-- Comments will be loaded here -->
                </div>
                <div class="comment-form">
                    <textarea class="comment-input" placeholder="Write a comment..." data-post-id="${post.id}"></textarea>
                    <button class="btn-post-comment" data-post-id="${post.id}">Post Comment</button>
                </div>
            </div>
        `;
        postsContainer.appendChild(postElement);
    });

    // Initialize icons for new elements
    lucide.createIcons();

    // Add event listeners for comment buttons
    document.querySelectorAll('.btn-view-comments').forEach(btn => {
        btn.addEventListener('click', async function() {
            const postId = this.getAttribute('data-post-id');
            const commentsSection = document.getElementById(`comments-${postId}`);

            if (commentsSection.style.display === 'none') {
                commentsSection.style.display = 'block';
                await loadComments(postId);
            } else {
                commentsSection.style.display = 'none';
            }
        });
    });

    // Add event listeners for post comment buttons
    document.querySelectorAll('.btn-post-comment').forEach(btn => {
        btn.addEventListener('click', async function() {
            const postId = this.getAttribute('data-post-id');
            const commentInput = document.querySelector(`.comment-input[data-post-id="${postId}"]`);
            const content = commentInput.value.trim();

            if (!content) {
                alert('Please enter a comment');
                return;
            }

            await postComment(postId, content, commentInput);
        });
    });

    // Add event listeners for comment input (Enter key)
    document.querySelectorAll('.comment-input').forEach(input => {
        input.addEventListener('keypress', async function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const postId = this.getAttribute('data-post-id');
                const content = this.value.trim();

                if (!content) return;

                await postComment(postId, content, this);
            }
        });
    });

    // Add event listeners for like buttons
    document.querySelectorAll('.btn-like').forEach(btn => {
        btn.addEventListener('click', async function () {
            const postId = this.getAttribute('data-post-id');
            const likeCountElement = this.querySelector('.like-count');
            const iconElement = this.querySelector('i');
            const isLiked = this.classList.contains('liked');

            // Optimistic UI update
            this.classList.toggle('liked');
            const currentLikes = parseInt(likeCountElement.textContent);
            likeCountElement.textContent = isLiked ? currentLikes - 1 : currentLikes + 1;
            if (iconElement) {
                iconElement.setAttribute('fill', isLiked ? 'none' : 'currentColor');
            }

            try {
                const res = await fetch(`/api/posts/like?post_id=${postId}`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (!res.ok) {
                    // Revert UI if API call fails
                    this.classList.toggle('liked');
                    likeCountElement.textContent = isLiked ? currentLikes : currentLikes - 1;
                    if (iconElement) {
                        iconElement.setAttribute('fill', isLiked ? 'currentColor' : 'none');
                    }
                    throw new Error('Failed to like post');
                } else {
                    // Refresh notifications for the post owner
                    if (window.refreshNotifications) {
                        window.refreshNotifications();
                    }
                }
            } catch (err) {
                console.error('Error liking post:', err);
            }
        });
    });
}

    async function loadComments(postId) {
        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                credentials: 'include'
            });

            if (res.ok) {
                const comments = await res.json();
                await renderComments(postId, comments);
            }
        } catch (err) {
            console.error('Error loading comments:', err);
        }
    }

    async function renderComments(postId, comments) {
        const commentsContainer = document.getElementById(`comments-container-${postId}`);

        if (comments.length === 0) {
            commentsContainer.innerHTML = '<p class="no-comments">No comments yet. Be the first to comment!</p>';
            return;
        }

        // Get all user profiles to update comment names for everyone
        let allUserProfiles = {};
        try {
            const timestamp = new Date().getTime();
            const usersRes = await fetch(`/api/users?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });
            if (usersRes.ok) {
                const users = await usersRes.json();
                users.forEach(user => {
                    allUserProfiles[user.email] = user;
                });
            }
        } catch (err) {
            console.error('Error fetching user profiles:', err);
        }

        commentsContainer.innerHTML = comments.map(comment => {
            // Use updated username for ALL users' comments
            let displayName = comment.userName || 'Anonymous';
            const userProfile = allUserProfiles[comment.userEmail];
            if (userProfile) {
                displayName = `${userProfile.firstname} ${userProfile.lastname}`;
            }

            return `
                <div class="comment" data-comment-id="${comment.id}">
                    <div class="comment-header">
                        <div class="comment-avatar"><i data-lucide="user"></i></div>
                        <div>
                            <span class="comment-user">${displayName}</span>
                            <span class="comment-time">${formatDate(comment.createdAt)}</span>
                        </div>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                </div>
            `;
        }).join('');

        lucide.createIcons();
    }

    async function postComment(postId, content, inputElement) {
        // Get current user info
        let currentUser = '';
        let currentUserName = '';
        try {
            const authRes = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (authRes.ok) {
                const authData = await authRes.json();
                currentUser = authData.email;
            }

            const profileRes = await fetch('/api/profile', {
                credentials: 'include'
            });
            if (profileRes.ok) {
                const profileData = await profileRes.json();
                currentUserName = `${profileData.user.firstname} ${profileData.user.lastname}`;
            }
        } catch (err) {
            console.error('Error fetching user info:', err);
        }

        // Update comment count optimistically
        const commentCountElement = document.querySelector(`.btn-view-comments[data-post-id="${postId}"] .comment-count`);
        if (commentCountElement) {
            const currentCount = parseInt(commentCountElement.textContent);
            commentCountElement.textContent = currentCount + 1;
        }

        // Clear input immediately
        inputElement.value = '';

        // Create temporary comment for optimistic UI update
        const tempComment = {
            id: 'temp-' + Date.now(),
            content: content,
            userName: currentUserName,
            createdAt: new Date().toISOString(),
            userEmail: currentUser
        };

        // Add comment to UI immediately
        const commentsContainer = document.getElementById(`comments-container-${postId}`);
        const noCommentsMsg = commentsContainer.querySelector('.no-comments');
        if (noCommentsMsg) {
            noCommentsMsg.remove();
        }

        const commentElement = document.createElement('div');
        commentElement.className = 'comment';
        commentElement.setAttribute('data-comment-id', tempComment.id);
        commentElement.innerHTML = `
            <div class="comment-header">
                <div class="comment-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="comment-user">${tempComment.userName}</span>
                    <span class="comment-time">${formatDate(tempComment.createdAt)}</span>
                </div>
            </div>
            <div class="comment-content">${tempComment.content}</div>
        `;
        // Add new comment at the top (newest first)
        commentsContainer.insertBefore(commentElement, commentsContainer.firstChild);
        lucide.createIcons();

        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    content: content
                })
            });

            if (res.ok) {
                // Refresh notifications for the post owner
                if (window.refreshNotifications) {
                    window.refreshNotifications();
                }
                // Reload comments to get the real comment with proper ID
                await loadComments(postId);
            } else {
                throw new Error('Failed to post comment');
            }
        } catch (err) {
            console.error('Error posting comment:', err);
            // Remove the temporary comment on error
            commentElement.remove();

            // Revert comment count
            if (commentCountElement) {
                const currentCount = parseInt(commentCountElement.textContent);
                commentCountElement.textContent = currentCount - 1;
            }

            // Restore input value
            inputElement.value = content;
            alert('Failed to post comment. Please try again.');
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = Math.floor((now - date) / 1000);
        if (diff < 60) return 'just now';
        if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
        return `${Math.floor(diff / 86400)} days ago`;
    }
});
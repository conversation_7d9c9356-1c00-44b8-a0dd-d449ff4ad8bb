package controller

import (
	"net/http"
	"prj/model"
	"prj/utils/httpResp"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func LikePost(w http.ResponseWriter, r *http.Request) {
	postID := r.URL.Query().Get("post_id")
	if postID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	var post model.Post

	// Look for the post only in PostCollection
	err := model.PostCollection.FindOne(r.Context(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch post")
		return
	}

	// Check if user already liked the post
	alreadyLiked := false
	for _, likedBy := range post.LikedBy {
		if likedBy == email {
			alreadyLiked = true
			break
		}
	}

	if alreadyLiked {
		// Unlike the post
		_, err := model.PostCollection.UpdateOne(
			r.Context(),
			bson.M{"_id": postID},
			bson.M{
				"$pull": bson.M{"likedBy": email},
				"$inc":  bson.M{"likes": -1},
			},
		)
		if err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to unlike post")
			return
		}
		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "unliked"})
	} else {
		// Like the post
		_, err = model.PostCollection.UpdateOne(
			r.Context(),
			bson.M{"_id": postID},
			bson.M{
				"$addToSet": bson.M{"likedBy": email},
				"$inc":      bson.M{"likes": 1},
			},
		)
		if err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to like post")
			return
		}

		// Get user details for notification
		var user model.User
		err = model.UserCollection.FindOne(r.Context(), bson.M{"email": email}).Decode(&user)
		if err == nil {
			// Create notification for post owner (don't notify if user likes their own post)
			actorName := user.FirstName + " " + user.LastName
			postContentPreview := post.Content
			if len(postContentPreview) > 50 {
				postContentPreview = postContentPreview[:50] + "..."
			}
			// Note: We store the current name, but the frontend will fetch fresh data when displaying
			CreateNotification(post.UserEmail, email, actorName, "like", postID, postContentPreview, "")
		}

		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "liked"})
	}
}

package model

import (
	"time"
)

type Notification struct {
	ID          string    `json:"id" bson:"_id"`
	UserEmail   string    `json:"userEmail" bson:"userEmail"`     // Who receives the notification
	ActorEmail  string    `json:"actorEmail" bson:"actorEmail"`   // Who performed the action
	ActorName   string    `json:"actorName" bson:"actorName"`     // Name of who performed the action
	Type        string    `json:"type" bson:"type"`               // "like" or "comment"
	PostID      string    `json:"postId" bson:"postId"`           // Which post
	PostContent string    `json:"postContent" bson:"postContent"` // Preview of post content
	CommentText string    `json:"commentText,omitempty" bson:"commentText,omitempty"` // Comment text if type is comment
	IsRead      bool      `json:"isRead" bson:"isRead"`
	CreatedAt   time.Time `json:"createdAt" bson:"createdAt"`
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - MyApp</title>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="stylesheet" href="css/general.css">
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body>
    <nav class="navbar">
        <a href="home.html" class="navbar-logo">MyApp</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="home.html" class="nav-link">Home</a></li>
            <li class="nav-item"><a href="video.html" class="nav-link ">Videos</a></li>
            <li class="nav-item"><a href="users.html" class="nav-link">Users</a></li>
        </ul>
        <div class="navbar-center">
            <div class="navbar-search">
                <i data-lucide="search" class="search-icon"></i>
                <input type="text" id="navbarSearchInput" class="search-input" placeholder="Search for people...">
                <div id="navbarSearchDropdown" class="search-dropdown">
                    <div id="navbarSearchResults"></div>
                </div>
            </div>
        </div>
        <div class="user-menu">
            <button id="userBtn" class="user-btn">
                <span id="userEmail"></span>
                <i data-lucide="chevron-down"></i>
            </button>
            <div id="dropdownMenu" class="dropdown-menu">
                <a href="profile.html" class="dropdown-link">Profile</a>
                <a href="settings.html" class="dropdown-link">Settings</a>
                <a href="#" id="logoutBtn" class="dropdown-link">Logout</a>
            </div>
        </div>
    </nav>

    <main class="settings-container">
        <h1>Account Settings</h1>
        
        <div class="settings-section">
            <h2>Profile Information</h2>
            <form id="profileForm">
                <div class="form-group">
                    <label for="firstName">First Name</label>
                    <input type="text" id="firstName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <input type="text" id="lastName" class="form-control">
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" class="form-control" readonly>
                </div>
                <button type="submit" class="btn-primary">Update Profile</button>
            </form>
        </div>

        <div class="settings-section">
            <h2>Change Password</h2>
            <form id="passwordForm">
                <div class="form-group">
                    <label for="currentPassword">Current Password</label>
                    <div class="password-input-container">
                        <input type="password" id="currentPassword" class="form-control" required>
                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                            <i data-lucide="eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <div class="password-input-container">
                        <input type="password" id="newPassword" class="form-control" required>
                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                            <i data-lucide="eye"></i>
                        </button>
                    </div>
                    <div class="password-requirements">
                        <ul>
                            <li id="req-length"><span class="req-icon">✗</span> At least 8 characters</li>
                            <li id="req-uppercase"><span class="req-icon">✗</span> At least 1 uppercase letter</li>
                            <li id="req-number"><span class="req-icon">✗</span> At least 1 number</li>
                            <li id="req-special"><span class="req-icon">✗</span> At least 1 special character</li>
                        </ul>
                    </div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm New Password</label>
                    <div class="password-input-container">
                        <input type="password" id="confirmPassword" class="form-control" required>
                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                            <i data-lucide="eye"></i>
                        </button>
                    </div>
                </div>
                <button type="submit" class="btn-primary">Change Password</button>
            </form>
        </div>
    </main>

    <footer class="footer">
        <p>&copy; GCIT NEWS</p>
    </footer>

    <script src="js/sticky-navbar.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/navbar-search.js"></script>
    <script>lucide.createIcons();</script>
</body>

</html>
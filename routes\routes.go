package routes

import (
	"log"
	"net/http"
	"prj/controller"
	"prj/middleware"
	"prj/utils/httpResp"

	"github.com/gorilla/mux"
)

func InitializeRoutes() {
	router := mux.NewRouter()

	// Auth Routes
	auth := router.PathPrefix("/").Subrouter()
	auth.HandleFunc("/signup", controller.Signup).Methods("POST")
	auth.HandleFunc("/login", controller.Login).Methods("POST")
	auth.HandleFunc("/logout", controller.Logout).Methods("POST")
	auth.HandleFunc("/refresh", controller.Refresh).Methods("POST")
	auth.HandleFunc("/check-email", controller.CheckEmail).Methods("GET")

	// Protected API Routes
	api := router.PathPrefix("/api").Subrouter()
	api.Use(middleware.AuthMiddleware)
	api.Use(middleware.RateLimitMiddleware)

	api.HandleFunc("/check-auth", func(w http.ResponseWriter, r *http.Request) {
		email := r.Context().Value("user_email")
		if email == nil {
			controller.Logout(w, r)
			return
		}
		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{
			"email": email.(string),
		})
	}).Methods("GET")

	api.HandleFunc("/posts", controller.CreatePost).Methods("POST")
	api.HandleFunc("/posts", controller.GetPosts).Methods("GET")
	api.HandleFunc("/posts", controller.UpdatePost).Methods("PUT")
	api.HandleFunc("/posts", controller.DeletePost).Methods("DELETE")
	api.HandleFunc("/posts/like", controller.LikePost).Methods("POST")
	api.HandleFunc("/posts/{postId}/comments", controller.AddComment).Methods("POST")
	api.HandleFunc("/posts/{postId}/comments", controller.GetComments).Methods("GET")
	api.HandleFunc("/posts/{postId}/comments/{commentId}/like", controller.LikeComment).Methods("POST")
	api.HandleFunc("/posts/{postId}/comments/{commentId}/replies", controller.ReplyToComment).Methods("POST")
	api.HandleFunc("/posts/{postId}/comments/{commentId}/replies/{replyId}/like", controller.LikeReply).Methods("POST")
	api.HandleFunc("/notifications", controller.GetNotifications).Methods("GET")
	api.HandleFunc("/notifications/count", controller.GetUnreadNotificationCount).Methods("GET")
	api.HandleFunc("/notifications/read", controller.MarkNotificationAsRead).Methods("POST")
	api.HandleFunc("/notifications/read-all", controller.MarkAllNotificationsAsRead).Methods("POST")
	api.HandleFunc("/activity", controller.GetRecentActivity).Methods("GET")
	api.HandleFunc("/profile", controller.GetUserProfile).Methods("GET")
	api.HandleFunc("/users", controller.GetAllUsers).Methods("GET")
	api.HandleFunc("/users/search", controller.SearchUsers).Methods("GET")
	api.HandleFunc("/users/{email}/posts", controller.GetUserPosts).Methods("GET")
	api.HandleFunc("/profile/update", controller.UpdateProfile).Methods("PUT")
	api.HandleFunc("/profile/change-password", controller.ChangePassword).Methods("POST")

	// Serve uploaded files
	router.PathPrefix("/uploads/").Handler(http.StripPrefix("/uploads/", http.FileServer(http.Dir("uploads"))))

	// Serve static files
	router.PathPrefix("/").Handler(http.StripPrefix("/", http.FileServer(http.Dir("./views/"))))

	// Explicit HTML routes
	router.HandleFunc("/home.html", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./views/home.html")
	})
	router.HandleFunc("/profile.html", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./views/profile.html")
	})

	// Start Server
	port := ":8080"
	log.Printf("Server started on http://localhost%s\n", port)
	log.Fatal(http.ListenAndServe(port, router))
}

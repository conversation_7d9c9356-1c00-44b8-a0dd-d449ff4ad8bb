// Infinite Scroll Implementation
class InfiniteScroll {
    constructor(options = {}) {
        this.container = options.container || document.body;
        this.loadMore = options.loadMore || (() => {});
        this.hasMore = options.hasMore || (() => true);
        this.threshold = options.threshold || 200; // pixels from bottom
        this.loading = false;
        this.enabled = true;
        this.page = 1;
        this.limit = options.limit || 10;
        
        this.init();
    }

    init() {
        this.createLoadingIndicator();
        this.bindEvents();
    }

    createLoadingIndicator() {
        this.loadingIndicator = document.createElement('div');
        this.loadingIndicator.className = 'infinite-scroll-loading';
        this.loadingIndicator.innerHTML = `
            <div class="spinner"></div>
            <p>Loading more content...</p>
        `;
        this.loadingIndicator.style.display = 'none';
        
        // Append to container or its parent
        if (this.container.parentNode) {
            this.container.parentNode.appendChild(this.loadingIndicator);
        } else {
            document.body.appendChild(this.loadingIndicator);
        }
    }

    bindEvents() {
        this.scrollHandler = this.throttle(() => {
            if (this.shouldLoadMore()) {
                this.loadMoreContent();
            }
        }, 100);

        window.addEventListener('scroll', this.scrollHandler);
        window.addEventListener('resize', this.scrollHandler);
    }

    shouldLoadMore() {
        if (!this.enabled || this.loading || !this.hasMore()) {
            return false;
        }

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        return (scrollTop + windowHeight) >= (documentHeight - this.threshold);
    }

    async loadMoreContent() {
        if (this.loading) return;

        this.loading = true;
        this.showLoading();

        try {
            const result = await this.loadMore(this.page, this.limit);
            
            if (result && result.length > 0) {
                this.page++;
                this.appendContent(result);
            }
        } catch (error) {
            console.error('Error loading more content:', error);
            this.showError();
        } finally {
            this.loading = false;
            this.hideLoading();
        }
    }

    appendContent(items) {
        items.forEach((item, index) => {
            const element = this.createContentElement(item);
            if (element) {
                // Add stagger animation
                element.classList.add('stagger-item');
                element.style.animationDelay = `${index * 0.1}s`;
                this.container.appendChild(element);
            }
        });

        // Observe new lazy images
        if (window.LazyLoadingUtils) {
            window.LazyLoadingUtils.observeNewContent(this.container);
        }
    }

    createContentElement(item) {
        // This should be overridden by specific implementations
        const div = document.createElement('div');
        div.textContent = JSON.stringify(item);
        return div;
    }

    showLoading() {
        this.loadingIndicator.style.display = 'block';
    }

    hideLoading() {
        this.loadingIndicator.style.display = 'none';
    }

    showError() {
        this.loadingIndicator.innerHTML = `
            <p style="color: #ff4444;">Failed to load more content. <button onclick="this.parentNode.parentNode.infiniteScroll.retry()">Retry</button></p>
        `;
        this.loadingIndicator.infiniteScroll = this;
    }

    retry() {
        this.createLoadingIndicator();
        this.loadMoreContent();
    }

    enable() {
        this.enabled = true;
    }

    disable() {
        this.enabled = false;
        this.hideLoading();
    }

    reset() {
        this.page = 1;
        this.loading = false;
        this.enabled = true;
        this.hideLoading();
    }

    destroy() {
        window.removeEventListener('scroll', this.scrollHandler);
        window.removeEventListener('resize', this.scrollHandler);
        if (this.loadingIndicator && this.loadingIndicator.parentNode) {
            this.loadingIndicator.parentNode.removeChild(this.loadingIndicator);
        }
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
}

// Posts Infinite Scroll
class PostsInfiniteScroll extends InfiniteScroll {
    constructor(container, renderFunction) {
        super({
            container: container,
            loadMore: (page, limit) => this.loadMorePosts(page, limit),
            hasMore: () => this.hasMorePosts,
            limit: 5
        });
        
        this.renderFunction = renderFunction;
        this.hasMorePosts = true;
        this.totalLoaded = 0;
    }

    async loadMorePosts(page, limit) {
        const offset = (page - 1) * limit;
        const response = await fetch(`/api/posts?offset=${offset}&limit=${limit}`, {
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to load posts');
        }

        const posts = await response.json();
        
        // Check if we have more posts
        this.hasMorePosts = posts.length === limit;
        this.totalLoaded += posts.length;

        return posts;
    }

    createContentElement(post) {
        if (this.renderFunction) {
            return this.renderFunction(post);
        }
        return null;
    }
}

// Users Infinite Scroll
class UsersInfiniteScroll extends InfiniteScroll {
    constructor(container, renderFunction) {
        super({
            container: container,
            loadMore: (page, limit) => this.loadMoreUsers(page, limit),
            hasMore: () => this.hasMoreUsers,
            limit: 12
        });
        
        this.renderFunction = renderFunction;
        this.hasMoreUsers = true;
        this.searchQuery = '';
    }

    async loadMoreUsers(page, limit) {
        const offset = (page - 1) * limit;
        let url = `/api/users?offset=${offset}&limit=${limit}`;
        
        if (this.searchQuery) {
            url += `&search=${encodeURIComponent(this.searchQuery)}`;
        }

        const response = await fetch(url, {
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to load users');
        }

        const users = await response.json();
        
        // Check if we have more users
        this.hasMoreUsers = users.length === limit;

        return users;
    }

    createContentElement(user) {
        if (this.renderFunction) {
            return this.renderFunction(user);
        }
        return null;
    }

    setSearchQuery(query) {
        this.searchQuery = query;
        this.reset();
    }
}

// Utility functions
window.InfiniteScrollUtils = {
    // Create posts infinite scroll
    createPostsScroll: function(container, renderFunction) {
        return new PostsInfiniteScroll(container, renderFunction);
    },

    // Create users infinite scroll
    createUsersScroll: function(container, renderFunction) {
        return new UsersInfiniteScroll(container, renderFunction);
    },

    // Generic infinite scroll
    create: function(options) {
        return new InfiniteScroll(options);
    }
};

// Auto-initialize for common containers
document.addEventListener('DOMContentLoaded', () => {
    // Auto-detect and initialize infinite scroll for posts
    const postsContainer = document.getElementById('postsContainer');
    if (postsContainer && window.location.pathname.includes('home.html')) {
        // Will be initialized by home.js with proper render function
    }

    // Auto-detect and initialize infinite scroll for users
    const usersContainer = document.getElementById('usersContainer');
    if (usersContainer && window.location.pathname.includes('users.html')) {
        // Will be initialized by users.js with proper render function
    }
});

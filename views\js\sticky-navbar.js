// Sticky Navbar Enhancement
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar');
    
    if (!navbar) return;
    
    // Ensure navbar is sticky
    function ensureStickyNavbar() {
        // Force sticky positioning
        navbar.style.position = 'sticky';
        navbar.style.top = '0';
        navbar.style.zIndex = '1000';
        navbar.style.width = '100%';
        
        // Add a class for additional styling if needed
        navbar.classList.add('navbar-sticky');
    }
    
    // Apply sticky behavior immediately
    ensureStickyNavbar();
    
    // Fallback: Use fixed positioning if sticky doesn't work
    let isSticky = false;
    const navbarHeight = navbar.offsetHeight;
    
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 0 && !isSticky) {
            // Check if navbar is actually sticking
            const navbarRect = navbar.getBoundingClientRect();
            
            // If navbar is not at the top when scrolled, force fixed positioning
            if (navbarRect.top < 0) {
                navbar.style.position = 'fixed';
                navbar.style.top = '0';
                navbar.style.left = '0';
                navbar.style.right = '0';
                navbar.classList.add('navbar-fixed');
                
                // Add padding to body to prevent content jump
                document.body.style.paddingTop = navbarHeight + 'px';
                isSticky = true;
            }
        } else if (scrollTop === 0 && isSticky) {
            // Reset to sticky when at top
            navbar.style.position = 'sticky';
            navbar.style.left = '';
            navbar.style.right = '';
            navbar.classList.remove('navbar-fixed');
            document.body.style.paddingTop = '';
            isSticky = false;
        }
    }
    
    // Listen for scroll events
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Check on resize
    window.addEventListener('resize', function() {
        if (isSticky) {
            document.body.style.paddingTop = navbar.offsetHeight + 'px';
        }
    });
});

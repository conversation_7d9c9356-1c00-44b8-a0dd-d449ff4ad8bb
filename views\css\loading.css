/* Loading States & Skeleton Components */

/* Skeleton Loading Animations */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

/* Skeleton Post */
.skeleton-post {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.skeleton-post-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 1rem;
}

.skeleton-user-info {
    flex: 1;
}

.skeleton-username {
    height: 16px;
    width: 120px;
    margin-bottom: 8px;
}

.skeleton-time {
    height: 12px;
    width: 80px;
}

.skeleton-content {
    height: 60px;
    margin-bottom: 1rem;
}

.skeleton-media {
    height: 200px;
    margin-bottom: 1rem;
}

.skeleton-actions {
    display: flex;
    gap: 1rem;
}

.skeleton-action {
    height: 32px;
    width: 80px;
}

/* Skeleton User Card */
.skeleton-user-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
}

.skeleton-user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 1rem;
}

.skeleton-user-name {
    height: 18px;
    width: 80%;
    margin: 0 auto 8px;
}

.skeleton-user-email {
    height: 14px;
    width: 60%;
    margin: 0 auto;
}

/* Skeleton Activity Item */
.skeleton-activity {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.skeleton-activity:last-child {
    border-bottom: none;
}

.skeleton-activity-text {
    height: 16px;
    width: 90%;
    margin-bottom: 8px;
}

.skeleton-activity-time {
    height: 12px;
    width: 60px;
}

/* Loading Spinners */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4a6cf7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-small {
    width: 16px;
    height: 16px;
    border-width: 1.5px;
}

.spinner-large {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    overflow: hidden;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    color: #4a6cf7;
}

.loading-content .spinner {
    margin-bottom: 1rem;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4a6cf7, #6c7cf7);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-bar.indeterminate {
    width: 30%;
    animation: progress-indeterminate 2s infinite;
}

@keyframes progress-indeterminate {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(400%);
    }
}

/* Button Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

/* Ensure text is completely hidden during loading */
.btn-loading * {
    opacity: 0 !important;
}

/* Lazy Loading Placeholder */
.lazy-loading {
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 0.9rem;
}

.lazy-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #999;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

/* Infinite Scroll Loading */
.infinite-scroll-loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.infinite-scroll-loading .spinner {
    margin-bottom: 0.5rem;
}

/* Posts Loading */
.posts-loading {
    text-align: center;
    padding: 4rem 2rem;
    color: #4a6cf7;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.posts-loading .spinner-large {
    margin-bottom: 1rem;
}

.posts-loading p {
    font-size: 1.1rem;
    margin: 0;
}

/* Fade In Animation for New Content */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse Animation for Loading Elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

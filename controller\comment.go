package controller

import (
	"context"
	"encoding/json"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"
	"time"

	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CommentRequest struct {
	Content string `json:"content"`
}

func AddComment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	postID := vars["postId"]

	if postID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id is required")
		return
	}

	var commentReq CommentRequest
	if err := json.NewDecoder(r.Body).Decode(&commentReq); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if commentReq.Content == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Comment content is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user details")
		return
	}

	// Check if post exists
	var post model.Post
	err = model.PostCollection.FindOne(context.TODO(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch post")
		return
	}

	// Create new comment
	comment := model.Comment{
		ID:        primitive.NewObjectID().Hex(),
		Content:   commentReq.Content,
		UserEmail: email,
		UserName:  user.FirstName + " " + user.LastName,
		CreatedAt: time.Now(),
		Likes:     0,
		LikedBy:   []string{},
		Replies:   []model.Reply{},
	}

	// Add comment to post
	_, err = model.PostCollection.UpdateOne(
		context.TODO(),
		bson.M{"_id": postID},
		bson.M{"$push": bson.M{"comments": comment}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to add comment")
		return
	}

	// Create notification for post owner (don't notify if user comments on their own post)
	postContentPreview := post.Content
	if len(postContentPreview) > 50 {
		postContentPreview = postContentPreview[:50] + "..."
	}
	commentPreview := comment.Content
	if len(commentPreview) > 100 {
		commentPreview = commentPreview[:100] + "..."
	}
	// Note: We store the current name, but the frontend will fetch fresh data when displaying
	CreateNotification(post.UserEmail, email, comment.UserName, "comment", postID, postContentPreview, commentPreview)

	httpResp.RespondWithJSON(w, http.StatusCreated, comment)
}

func GetComments(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	postID := vars["postId"]

	if postID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id is required")
		return
	}

	var post model.Post
	err := model.PostCollection.FindOne(context.TODO(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch post")
		return
	}

	// Ensure comments is never nil
	if post.Comments == nil {
		post.Comments = []model.Comment{}
	}

	httpResp.RespondWithJSON(w, http.StatusOK, post.Comments)
}

func LikeComment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	postID := vars["postId"]
	commentID := vars["commentId"]

	if postID == "" || commentID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id and comment_id are required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user details")
		return
	}

	// Find the post and comment
	var post model.Post
	err = model.PostCollection.FindOne(context.TODO(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch post")
		return
	}

	// Find the comment in the post
	commentIndex := -1
	for i, comment := range post.Comments {
		if comment.ID == commentID {
			commentIndex = i
			break
		}
	}

	if commentIndex == -1 {
		httpResp.RespondWithError(w, http.StatusNotFound, "Comment not found")
		return
	}

	// Check if user already liked the comment
	alreadyLiked := false
	for _, likedBy := range post.Comments[commentIndex].LikedBy {
		if likedBy == email {
			alreadyLiked = true
			break
		}
	}

	if alreadyLiked {
		// Unlike the comment
		_, err := model.PostCollection.UpdateOne(
			context.TODO(),
			bson.M{"_id": postID, "comments.id": commentID},
			bson.M{
				"$pull": bson.M{"comments.$.likedBy": email},
				"$inc":  bson.M{"comments.$.likes": -1},
			},
		)
		if err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to unlike comment")
			return
		}
		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "unliked"})
	} else {
		// Like the comment
		_, err = model.PostCollection.UpdateOne(
			context.TODO(),
			bson.M{"_id": postID, "comments.id": commentID},
			bson.M{
				"$addToSet": bson.M{"comments.$.likedBy": email},
				"$inc":      bson.M{"comments.$.likes": 1},
			},
		)
		if err != nil {
			httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to like comment")
			return
		}

		// Create notification for comment owner (don't notify if user likes their own comment)
		actorName := user.FirstName + " " + user.LastName
		commentContent := post.Comments[commentIndex].Content
		if len(commentContent) > 50 {
			commentContent = commentContent[:50] + "..."
		}
		CreateNotification(post.Comments[commentIndex].UserEmail, email, actorName, "comment_like", postID, commentContent, "")

		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "liked"})
	}
}

func ReplyToComment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	postID := vars["postId"]
	commentID := vars["commentId"]

	if postID == "" || commentID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id and comment_id are required")
		return
	}

	var replyReq CommentRequest
	if err := json.NewDecoder(r.Body).Decode(&replyReq); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if replyReq.Content == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "Reply content is required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user details")
		return
	}

	// Check if post exists
	var post model.Post
	err = model.PostCollection.FindOne(context.TODO(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch post")
		return
	}

	// Find the comment in the post
	commentIndex := -1
	for i, comment := range post.Comments {
		if comment.ID == commentID {
			commentIndex = i
			break
		}
	}

	if commentIndex == -1 {
		httpResp.RespondWithError(w, http.StatusNotFound, "Comment not found")
		return
	}

	// Create new reply
	reply := model.Reply{
		ID:        primitive.NewObjectID().Hex(),
		Content:   replyReq.Content,
		UserEmail: email,
		UserName:  user.FirstName + " " + user.LastName,
		CreatedAt: time.Now(),
		Likes:     0,
		LikedBy:   []string{},
	}

	// Add reply to comment
	_, err = model.PostCollection.UpdateOne(
		context.TODO(),
		bson.M{"_id": postID, "comments.id": commentID},
		bson.M{"$push": bson.M{"comments.$.replies": reply}},
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to add reply")
		return
	}

	// Create notification for comment owner (don't notify if user replies to their own comment)
	commentContent := post.Comments[commentIndex].Content
	if len(commentContent) > 50 {
		commentContent = commentContent[:50] + "..."
	}
	replyPreview := reply.Content
	if len(replyPreview) > 100 {
		replyPreview = replyPreview[:100] + "..."
	}
	CreateNotification(post.Comments[commentIndex].UserEmail, email, reply.UserName, "comment_reply", postID, commentContent, replyPreview)

	httpResp.RespondWithJSON(w, http.StatusCreated, reply)
}

func LikeReply(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	postID := vars["postId"]
	commentID := vars["commentId"]
	replyID := vars["replyId"]

	if postID == "" || commentID == "" || replyID == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "post_id, comment_id, and reply_id are required")
		return
	}

	email := r.Context().Value("user_email").(string)

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch user details")
		return
	}

	// Find the post, comment, and reply
	var post model.Post
	err = model.PostCollection.FindOne(context.TODO(), bson.M{"_id": postID}).Decode(&post)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "Post not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to fetch post")
		return
	}

	// Find the comment and reply
	commentIndex := -1
	replyIndex := -1
	for i, comment := range post.Comments {
		if comment.ID == commentID {
			commentIndex = i
			for j, reply := range comment.Replies {
				if reply.ID == replyID {
					replyIndex = j
					break
				}
			}
			break
		}
	}

	if commentIndex == -1 || replyIndex == -1 {
		httpResp.RespondWithError(w, http.StatusNotFound, "Comment or reply not found")
		return
	}

	// Check if user already liked the reply
	alreadyLiked := false
	for _, likedBy := range post.Comments[commentIndex].Replies[replyIndex].LikedBy {
		if likedBy == email {
			alreadyLiked = true
			break
		}
	}

	// Update the post in memory and then save it back
	if alreadyLiked {
		// Remove user from likedBy array
		newLikedBy := []string{}
		for _, likedBy := range post.Comments[commentIndex].Replies[replyIndex].LikedBy {
			if likedBy != email {
				newLikedBy = append(newLikedBy, likedBy)
			}
		}
		post.Comments[commentIndex].Replies[replyIndex].LikedBy = newLikedBy
		post.Comments[commentIndex].Replies[replyIndex].Likes--
	} else {
		// Add user to likedBy array
		post.Comments[commentIndex].Replies[replyIndex].LikedBy = append(post.Comments[commentIndex].Replies[replyIndex].LikedBy, email)
		post.Comments[commentIndex].Replies[replyIndex].Likes++
	}

	// Update the entire post
	_, err = model.PostCollection.ReplaceOne(
		context.TODO(),
		bson.M{"_id": postID},
		post,
	)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "Failed to update reply like")
		return
	}

	if alreadyLiked {
		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "unliked"})
	} else {
		// Create notification for reply owner (don't notify if user likes their own reply)
		actorName := user.FirstName + " " + user.LastName
		replyContent := post.Comments[commentIndex].Replies[replyIndex].Content
		if len(replyContent) > 50 {
			replyContent = replyContent[:50] + "..."
		}
		CreateNotification(post.Comments[commentIndex].Replies[replyIndex].UserEmail, email, actorName, "reply_like", postID, replyContent, "")

		httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "liked"})
	}
}

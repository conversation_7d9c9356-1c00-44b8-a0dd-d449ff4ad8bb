
/* Ensure proper stacking context for sticky navbar */
body {
    position: relative;
}

/* Navbar Styles */
.navbar {
    background-color: #4a6cf7;
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: -webkit-sticky; /* Safari support */
    position: sticky;
    top: 0;
    z-index: 1000;
    gap: 2rem;
    width: 100%;
    left: 0;
    right: 0;
}

/* Enhanced sticky states */
.navbar-sticky {
    position: sticky !important;
    top: 0 !important;
}

.navbar-fixed {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
}

.navbar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
}

.navbar-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 500px;
}

/* Search Styles */
.navbar-search {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: none;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 0.9rem;
    outline: none;
    transition: background-color 0.3s, box-shadow 0.3s;
}

.search-input:focus {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input::placeholder {
    color: #666;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    width: 16px;
    height: 16px;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
    margin-top: 0.5rem;
}

.search-dropdown.show {
    display: block;
    animation: fadeInDown 0.2s ease-out;
}

.search-result {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
}

.search-result:last-child {
    border-bottom: none;
}

.search-result:hover {
    background-color: #f8f9fa;
}

.search-result-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #4a6cf7;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
    font-size: 0.8rem;
}

.search-result-info {
    flex: 1;
}

.search-result-name {
    font-weight: 600;
    color: #333;
}

.no-search-results {
    padding: 1rem;
    text-align: center;
    color: #666;
    font-style: italic;
}

.search-section-header {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.remove-search {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
    opacity: 0;
}

.search-result:hover .remove-search {
    opacity: 1;
}

.remove-search:hover {
    background-color: #f0f0f0;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-item {
    margin: 0 0.5rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-menu {
    position: relative;
}

.notification-btn {
    background-color: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    width: 40px;
    height: 40px;
}

.notification-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.notification-btn i {
    width: 20px;
    height: 20px;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.notification-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 350px;
    max-height: 400px;
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.notification-dropdown.show {
    display: block;
    animation: fadeIn 0.2s ease-in-out;
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.mark-all-read-btn {
    background: none;
    border: none;
    color: #4a6cf7;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.mark-all-read-btn:hover {
    background-color: #f0f0f0;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #f0f7ff;
    border-left: 3px solid #4a6cf7;
}

.notification-avatar {
    width: 40px;
    height: 40px;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-avatar i {
    width: 20px;
    height: 20px;
    color: #666;
}

.notification-content {
    flex: 1;
}

.notification-text {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.8rem;
    color: #666;
}

.notification-post-preview {
    font-size: 0.8rem;
    color: #888;
    margin-top: 0.25rem;
    font-style: italic;
}

.no-notifications {
    padding: 2rem 1rem;
    text-align: center;
    color: #666;
    font-style: italic;
}

.user-menu {
    position: relative;
}

.user-btn {
    background-color: transparent;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.user-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 200px;
    z-index: 100;
    display: none;
    overflow: hidden;
}

.dropdown-menu.show {
    display: block;
    animation: fadeIn 0.2s ease-in-out;
}

.dropdown-link {
    display: block;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.dropdown-link:hover {
    background-color: #f8f9fa;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }

    .navbar-nav {
        flex-direction: column;
        align-items: center;
        margin: 0;
    }

    .nav-item {
        margin: 0.5rem 0;
    }

    .navbar-center {
        order: 2;
        max-width: 100%;
        margin: 0.5rem 0;
    }

    .navbar-search {
        max-width: 100%;
    }

    .navbar-right {
        order: 3;
        margin-top: 0;
    }

    .notification-dropdown {
        width: 300px;
    }

    .search-dropdown {
        left: -1rem;
        right: -1rem;
        margin-left: 1rem;
        margin-right: 1rem;
    }
}

.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
}

.profile-avatar i {
    width: 40px;
    height: 40px;
    color: #666;
}

.profile-info h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.profile-info p {
    color: #666;
    font-size: 1rem;
    margin: 0.2rem 0;
}

.profile-content h2 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.posts-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #4a6cf7;
}

/* Password requirements */
.password-requirements {
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.password-requirements ul {
    list-style: none;
    padding-left: 0;
    margin: 0.5rem 0 0;
}

.password-requirements li {
    margin-bottom: 0.3rem;
    color: #666;
    display: flex;
    align-items: center;
}

.req-icon {
    display: inline-block;
    width: 16px;
    text-align: center;
    margin-right: 5px;
    font-size: 0.8rem;
}

.password-requirements li.valid {
    color: #00C851;
}

.password-requirements li.valid .req-icon {
    color: #00C851;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: black;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-secondary {
    background: #f0f0f0;
    color: #333;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.btn-secondary:hover {
    background: #e0e0e0;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .profile-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .profile-container {
        padding: 0 0.5rem;
    }

    .modal-content {
        margin: 20% auto;
        width: 95%;
    }
}
const signupForm = document.getElementById('signupForm');
const fnameInput = document.getElementById('fname');
const lnameInput = document.getElementById('lname');
const emailInput = document.getElementById('email');
const pw1Input = document.getElementById('pw1');
const pw2Input = document.getElementById('pw2');
const submitBtn = document.querySelector('button[type="submit"]');

// Check password strength
function checkPasswordStrength(password) {
    let strength = 0;

    // Length check
    if (password.length >= 8) strength++;
    if (password.length >= 12) strength++;

    // Character variety checks
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    return strength;
}

// Update password strength meter
function updatePasswordStrength(password) {
    const strengthMeter = document.getElementById('password-strength-meter');
    if (!strengthMeter) return;

    const strength = checkPasswordStrength(password);
    let color = '#ff4444'; // Weak (red)
    let width = '25%';

    if (strength >= 4) {
        color = '#00C851'; // Strong (green)
        width = '100%';
    } else if (strength >= 2) {
        color = '#ffbb33'; // Medium (yellow)
        width = '50%';
    }

    strengthMeter.style.width = width;
    strengthMeter.style.backgroundColor = color;
}

// Validate password requirements
function validatePasswordRequirements(password) {
    const hasLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecial = /[^A-Za-z0-9]/.test(password);

    document.getElementById('req-length').classList.toggle('valid', hasLength);
    document.getElementById('req-uppercase').classList.toggle('valid', hasUppercase);
    document.getElementById('req-number').classList.toggle('valid', hasNumber);
    document.getElementById('req-special').classList.toggle('valid', hasSpecial);

    // Update icons
    const reqLengthIcon = document.querySelector('#req-length .req-icon');
    const reqUppercaseIcon = document.querySelector('#req-uppercase .req-icon');
    const reqNumberIcon = document.querySelector('#req-number .req-icon');
    const reqSpecialIcon = document.querySelector('#req-special .req-icon');

    reqLengthIcon.textContent = hasLength ? '✓' : '✗';
    reqUppercaseIcon.textContent = hasUppercase ? '✓' : '✗';
    reqNumberIcon.textContent = hasNumber ? '✓' : '✗';
    reqSpecialIcon.textContent = hasSpecial ? '✓' : '✗';

    return hasLength && hasUppercase && hasNumber && hasSpecial;
}

// Validate email format
function validateEmail(email) {
    const re = /^\S+@\S+\.\S+$/;
    return re.test(email);
}

// Validate name
function validateName(name) {
    return name.trim().length > 0;
}

// Validate password confirmation
function validateConfirmPassword(password, confirmPassword) {
    return password === confirmPassword;
}

// Toggle password visibility
function togglePasswordVisibility(input, button) {
    const isPassword = input.type === 'password';
    input.type = isPassword ? 'text' : 'password';
    const icon = button.querySelector('i');
    icon.setAttribute('data-lucide', isPassword ? 'eye-off' : 'eye');
    lucide.createIcons();
    
    // Also toggle the confirm password field
    const confirmPassword = document.getElementById('pw2');
    if (confirmPassword) {
        confirmPassword.type = input.type;
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert-error';
    errorDiv.textContent = message;

    const container = document.querySelector('.auth-container');
    const existingError = container.querySelector('.alert-error');
    if (existingError) {
        container.removeChild(existingError);
    }

    container.insertBefore(errorDiv, container.firstChild);
    setTimeout(() => {
        errorDiv.style.opacity = '0';
        setTimeout(() => {
            if (errorDiv.parentNode) {
                container.removeChild(errorDiv);
            }
        }, 500);
    }, 5000);
}

// Show success message
function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert-success';
    successDiv.textContent = message;

    const container = document.querySelector('.auth-container');
    const existingAlert = container.querySelector('.alert-success');
    if (existingAlert) {
        container.removeChild(existingAlert);
    }

    container.insertBefore(successDiv, container.firstChild);
    setTimeout(() => {
        successDiv.style.opacity = '0';
        setTimeout(() => {
            if (successDiv.parentNode) {
                container.removeChild(successDiv);
            }
        }, 500);
    }, 5000);
}

// Show field-specific error
function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    const existingError = field.nextElementSibling;
    if (existingError && existingError.classList.contains('field-error')) {
        existingError.remove();
    }

    if (message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        field.parentNode.insertBefore(errorElement, field.nextSibling);
        field.classList.add('error');
    } else {
        field.classList.remove('error');
    }
}

// Clear all field errors
function clearFieldErrors() {
    document.querySelectorAll('.field-error').forEach(el => el.remove());
    document.querySelectorAll('.form-group input').forEach(input => {
        input.classList.remove('error');
    });
}

// Reset button to default state
function resetButtonState() {
    submitBtn.disabled = false;
    submitBtn.classList.remove('btn-loading');
    submitBtn.innerHTML = 'Submit';
}

// Show button loading state
function showButtonLoading() {
    submitBtn.disabled = true;
    submitBtn.classList.add('btn-loading');
    submitBtn.textContent = 'Creating account...';
}

// Add shake animation to form elements
function addShakeAnimation(element) {
    element.classList.add('shake');
    setTimeout(() => {
        element.classList.remove('shake');
    }, 500);
}

async function handleSignup(event) {
    event.preventDefault();

    if (submitBtn.disabled) {
        return;
    }

    const fname = fnameInput.value.trim();
    const lname = lnameInput.value.trim();
    const email = emailInput.value.trim();
    const pw1 = pw1Input.value;
    const pw2 = pw2Input.value;
    
    clearFieldErrors();

    // Show loading state
    showButtonLoading();

    // Validate all fields
    let isValid = true;

    if (!validateName(fname)) {
        showFieldError('fname', 'This field is required');
        isValid = false;
    }

    if (!validateEmail(email)) {
        showFieldError('email', 'Please enter a valid email');
        isValid = false;
    }

    if (!validatePasswordRequirements(pw1)) {
        showFieldError('pw1', 'Password does not meet requirements');
        isValid = false;
    }

    if (!validateConfirmPassword(pw1, pw2)) {
        showFieldError('pw2', "Passwords don't match");
        isValid = false;
    }

    if (!isValid) {
        addShakeAnimation(signupForm);
        resetButtonState();
        return;
    }

    try {
        const response = await fetch('/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                firstname: fname,
                lastname: lname,
                email: email,
                password: pw1
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            if (errorData.error === "email already exists") {
                showFieldError('email', 'This email is already registered');
            } else {
                throw new Error(errorData.error || "Signup failed");
            }
            resetButtonState();
            return;
        }

        // Clear all fields on successful signup
        fnameInput.value = '';
        lnameInput.value = '';
        emailInput.value = '';
        pw1Input.value = '';
        pw2Input.value = '';
        updatePasswordStrength('');
        validatePasswordRequirements('');

        showSuccess("Signup successful! Redirecting to login...");
        document.querySelector('.auth-container').classList.add('disabled');

        // Add success animation to button
        submitBtn.classList.add('bounce-in');

        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    } catch (error) {
        showError(error.message);
        resetButtonState();
    }
}

// Reset form to initial state
function resetFormState() {
    // Reset button state
    resetButtonState();

    // Clear all inputs
    if (fnameInput) fnameInput.value = '';
    if (lnameInput) lnameInput.value = '';
    if (emailInput) emailInput.value = '';
    if (pw1Input) pw1Input.value = '';
    if (pw2Input) pw2Input.value = '';

    // Clear all errors
    clearFieldErrors();

    // Remove any success/error messages
    const container = document.querySelector('.auth-container');
    const existingAlerts = container.querySelectorAll('.alert-success, .alert-error');
    existingAlerts.forEach(alert => alert.remove());

    // Remove disabled state from container
    container.classList.remove('disabled');

    // Reset form classes
    if (signupForm) {
        signupForm.classList.remove('shake');
    }

    // Reset password strength indicator
    const strengthIndicator = document.querySelector('.password-strength');
    if (strengthIndicator) {
        strengthIndicator.innerHTML = '';
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Reset form state on page load
    resetFormState();
    // Initialize password toggle (only for pw1)
    const pw1Toggle = document.querySelector('#pw1 + .toggle-password');
    if (pw1Toggle) {
        pw1Toggle.addEventListener('click', () => {
            togglePasswordVisibility(pw1Input, pw1Toggle);
        });
    }

    // Form submission
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }

    // Real-time validation
    if (pw1Input) {
        pw1Input.addEventListener('input', function() {
            updatePasswordStrength(this.value);
            validatePasswordRequirements(this.value);
            if (validatePasswordRequirements(this.value)) {
                showFieldError('pw1', '');
            }
        });
    }

    if (pw2Input) {
        pw2Input.addEventListener('input', function() {
            const pw1 = pw1Input.value;
            if (validateConfirmPassword(pw1, this.value)) {
                showFieldError('pw2', '');
            }
        });
    }

    if (emailInput) {
        emailInput.addEventListener('input', function() {
            if (validateEmail(this.value)) {
                showFieldError('email', '');
            }
        });
    }

    if (fnameInput) {
        fnameInput.addEventListener('input', function() {
            if (validateName(this.value)) {
                showFieldError('fname', '');
            }
        });
    }
});

// Reset form when page becomes visible (e.g., when user navigates back)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        resetFormState();
    }
});

// Reset form when page is focused (additional safety)
window.addEventListener('focus', function() {
    resetFormState();
});
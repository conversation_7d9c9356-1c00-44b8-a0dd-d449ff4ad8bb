document.addEventListener('DOMContentLoaded', function () {
    lucide.createIcons();

    const userBtn = document.getElementById('userBtn');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const logoutBtn = document.getElementById('logoutBtn');
    const userEmailSpan = document.getElementById('userEmail');
    const usersContainer = document.getElementById('usersContainer');
    const searchInput = document.getElementById('searchInput');

    let usersInfiniteScroll = null;
    let allUsers = []; // Store all users for search
    let isInitialLoad = true;

    // Toggle dropdown
    userBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
    dropdownMenu.addEventListener('click', (e) => e.stopPropagation());

    logoutBtn.addEventListener('click', async function (e) {
        e.preventDefault();
        try {
            const res = await fetch('/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                // Use replace to avoid back button issues and full URL to bypass service worker
                window.location.replace(window.location.origin + '/index.html');
            } else {
                throw new Error('Logout failed');
            }
        } catch (err) {
            alert('Logout failed. Try again.');
        }
    });

    checkAuthStatus();
    showSkeletonUsers();
    loadInitialUsers();

    // Check if there's a search query in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('search');
    if (searchQuery) {
        searchInput.value = searchQuery;
        // Trigger search after users are loaded
        setTimeout(() => {
            searchInput.dispatchEvent(new Event('input'));
        }, 500);
    }

    async function checkAuthStatus() {
        try {
            const res = await fetch('/api/check-auth', {
                method: 'GET',
                credentials: 'include'
            });

            if (!res.ok) return window.location.href = 'index.html';

            const data = await res.json();
            userEmailSpan.textContent = data.email;
        } catch (err) {
            console.error('Auth check error:', err);
            window.location.href = 'index.html';
        }
    }

    // Skeleton loading functions
    function showSkeletonUsers() {
        if (!usersContainer) return;

        const skeletonHTML = Array(12).fill(0).map(() => `
            <div class="skeleton-user-card">
                <div class="skeleton skeleton-user-avatar"></div>
                <div class="skeleton skeleton-user-name"></div>
                <div class="skeleton skeleton-user-email"></div>
            </div>
        `).join('');

        usersContainer.innerHTML = skeletonHTML;
    }

    async function loadInitialUsers() {
        try {
            const res = await fetch('/api/users?limit=12', {
                credentials: 'include'
            });

            if (!res.ok) throw new Error('Failed to load users');

            const users = await res.json();
            allUsers = users; // Store for search
            renderUsers(users);

            // Initialize infinite scroll if we have full page of users
            if (window.InfiniteScrollUtils && users.length === 12) {
                usersInfiniteScroll = window.InfiniteScrollUtils.createUsersScroll(
                    usersContainer,
                    (user) => createUserElement(user)
                );
            }
        } catch (err) {
            console.error('Error loading users:', err);
            usersContainer.innerHTML = '<p class="no-users">Failed to load users. Please try again.</p>';
        } finally {
            isInitialLoad = false;
        }
    }

    async function loadUsers() {
        try {
            const res = await fetch('/api/users', {
                credentials: 'include'
            });

            if (!res.ok) throw new Error('Failed to load users');

            const users = await res.json();
            renderUsers(users);
        } catch (err) {
            console.error('Error loading users:', err);
            usersContainer.innerHTML = '<p class="no-users">Failed to load users. Please try again.</p>';
        }
    }

    function renderUsers(users) {
        usersContainer.innerHTML = '';

        if (users.length === 0) {
            usersContainer.innerHTML = '<p class="no-users">No users found.</p>';
            return;
        }

        users.forEach((user, index) => {
            const userElement = createUserElement(user);

            // Add stagger animation for initial load
            if (isInitialLoad) {
                userElement.classList.add('stagger-item');
                userElement.style.animationDelay = `${index * 0.05}s`;
            } else {
                userElement.classList.add('fade-in');
            }

            usersContainer.appendChild(userElement);
        });

        lucide.createIcons();

        // Observe new lazy images
        if (window.LazyLoadingUtils) {
            window.LazyLoadingUtils.observeNewContent(usersContainer);
        }
    }

    // Create individual user element (used by infinite scroll)
    function createUserElement(user) {
        const userElement = document.createElement('div');
        userElement.className = 'user-card card-hover btn-animated ripple';
        userElement.innerHTML = `
            <div class="user-avatar"><i data-lucide="user"></i></div>
            <div class="user-info">
                <div class="user-name">${user.firstname} ${user.lastname}</div>
                <div class="user-email">${user.email}</div>
            </div>
        `;

        userElement.addEventListener('click', () => {
            window.location.href = `user-profile.html?email=${encodeURIComponent(user.email)}`;
        });

        return userElement;
    }

    // Search functionality
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Debounce search
        searchTimeout = setTimeout(() => {
            if (searchTerm === '') {
                // Reset to show all users
                if (usersInfiniteScroll) {
                    usersInfiniteScroll.setSearchQuery('');
                    usersInfiniteScroll.enable();
                }
                renderUsers(allUsers.slice(0, 12)); // Show first 12 users
            } else {
                // Disable infinite scroll during search
                if (usersInfiniteScroll) {
                    usersInfiniteScroll.disable();
                }

                // Filter users locally for instant results
                const filteredUsers = allUsers.filter(user => {
                    const fullName = `${user.firstname} ${user.lastname}`.toLowerCase();
                    return fullName.startsWith(searchTerm);
                });

                renderUsers(filteredUsers);
            }
        }, 300);
    });
});
package model

import (
	"context"
	"log"
	"os"
	"regexp"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/joho/godotenv"
)

type User struct {
	FirstName string `json:"firstname" bson:"firstname"`
	LastName  string `json:"lastname" bson:"lastname"`
	Email     string `json:"email" bson:"email"`
	Password  string `json:"password" bson:"password"`
}

type Claims struct {
	Email string `json:"email"`
	jwt.RegisteredClaims
}

type TokenBlacklist struct {
	Token     string    `bson:"token"`
	Email     string    `bson:"email"`
	ExpiresAt time.Time `bson:"expiresAt"`
}

var (
	Client                   *mongo.Client
	UserCollection           *mongo.Collection
	TokenBlacklistCollection *mongo.Collection
	PostCollection           *mongo.Collection
	NotificationCollection   *mongo.Collection
	jwtSecret                []byte
)

func init() {
	if err := godotenv.Load(); err != nil {
		log.Fatal("Error loading .env file")
	}

	mongoURI := os.Getenv("MONGODB_URI")
	dbName := os.Getenv("DB_NAME")
	secret := os.Getenv("JWT_SECRET")

	if mongoURI == "" || dbName == "" || secret == "" {
		log.Fatal("MONGODB_URI, DB_NAME and JWT_SECRET must be set in .env file")
	}

	jwtSecret = []byte(secret)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var err error
	Client, err = mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}

	err = Client.Ping(ctx, nil)
	if err != nil {
		log.Fatal("Failed to ping MongoDB:", err)
	}

	log.Println("Connected to MongoDB Atlas!")
	UserCollection = Client.Database(dbName).Collection("users")
	TokenBlacklistCollection = Client.Database(dbName).Collection("token_blacklist")
	PostCollection = Client.Database(dbName).Collection("posts")
	NotificationCollection = Client.Database(dbName).Collection("notifications")

	// Create indexes
	_, err = UserCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Fatal("Failed to create index:", err)
	}

	_, err = TokenBlacklistCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "expiresAt", Value: 1}},
			Options: options.Index().SetExpireAfterSeconds(0),
		},
	)
	if err != nil {
		log.Fatal("Failed to create TTL index for token blacklist:", err)
	}

	_, err = PostCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "userEmail", Value: 1}},
		},
	)
	if err != nil {
		log.Fatal("Failed to create index for posts:", err)
	}

	_, err = NotificationCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "userEmail", Value: 1}, {Key: "createdAt", Value: -1}},
		},
	)
	if err != nil {
		log.Fatal("Failed to create index for notifications:", err)
	}

}

func IsEmailValid(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func IsPasswordStrong(password string) bool {
	if len(password) < 8 {
		return false
	}
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[^A-Za-z0-9]`).MatchString(password)
	return hasUpper && hasLower && hasNumber && hasSpecial
}

func GenerateJWTToken(email string) (string, string, error) {
	expirationTime := time.Now().Add(15 * time.Minute)
	refreshExpiration := time.Now().Add(7 * 24 * time.Hour)

	claims := &Claims{
		Email: email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	refreshClaims := &Claims{
		Email: email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(refreshExpiration),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)

	accessTokenString, err := accessToken.SignedString(jwtSecret)
	if err != nil {
		return "", "", err
	}

	refreshTokenString, err := refreshToken.SignedString(jwtSecret)
	if err != nil {
		return "", "", err
	}

	return accessTokenString, refreshTokenString, nil
}

func ValidateToken(tokenString string) (*Claims, error) {
	if IsTokenBlacklisted(tokenString) {
		return nil, jwt.ErrTokenInvalidClaims
	}

	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, jwt.ErrSignatureInvalid
	}

	return claims, nil
}

func InvalidateToken(tokenString string) error {
	claims, err := ValidateToken(tokenString)
	if err != nil {
		return err
	}

	_, err = TokenBlacklistCollection.InsertOne(context.Background(), TokenBlacklist{
		Token:     tokenString,
		ExpiresAt: claims.ExpiresAt.Time,
	})
	return err
}

func IsTokenBlacklisted(tokenString string) bool {
	count, err := TokenBlacklistCollection.CountDocuments(context.Background(),
		bson.M{"token": tokenString})
	return err == nil && count > 0
}

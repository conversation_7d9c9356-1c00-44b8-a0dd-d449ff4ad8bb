document.addEventListener('DOMContentLoaded', function () {
    lucide.createIcons();

    const userBtn = document.getElementById('userBtn');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const logoutBtn = document.getElementById('logoutBtn');
    const userEmailSpan = document.getElementById('userEmail');
    const profileName = document.getElementById('profileName');
    const profileEmail = document.getElementById('profileEmail');
    const postCount = document.getElementById('postCount');
    const postsContainer = document.getElementById('postsContainer');

    // Modal elements
    const editModal = document.getElementById('editModal');
    const editPostContent = document.getElementById('editPostContent');
    const updatePostBtn = document.getElementById('updatePostBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const closeModal = document.querySelector('.close-modal');
    const editMediaPreview = document.getElementById('editMediaPreview');

    let currentUserEmail = '';
    let currentEditingPostId = '';

    // Toggle dropdown
    userBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
    dropdownMenu.addEventListener('click', (e) => e.stopPropagation());

    logoutBtn.addEventListener('click', async function (e) {
        e.preventDefault();
        try {
            const res = await fetch('/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                // Use replace to avoid back button issues and full URL to bypass service worker
                window.location.replace(window.location.origin + '/index.html');
            } else {
                throw new Error('Logout failed');
            }
        } catch (err) {
            alert('Logout failed. Try again.');
        }
    });

    // Modal event listeners
    closeModal.addEventListener('click', () => editModal.style.display = 'none');
    cancelEditBtn.addEventListener('click', () => editModal.style.display = 'none');

    window.addEventListener('click', (e) => {
        if (e.target === editModal) {
            editModal.style.display = 'none';
        }
    });

    checkAuthStatus();
    loadProfile();

    // Global function to refresh user profile display
    window.refreshUserProfile = function() {
        updateUserProfile();
    };

    async function checkAuthStatus() {
        try {
            const res = await fetch('/api/check-auth', {
                method: 'GET',
                credentials: 'include'
            });

            if (!res.ok) return window.location.href = 'index.html';

            const data = await res.json();
            currentUserEmail = data.email;

            // Fetch user profile to show full name instead of email
            await updateUserProfile();
        } catch (err) {
            console.error('Auth check error:', err);
            window.location.href = 'index.html';
        }
    }

    // Update user profile display in navbar
    async function updateUserProfile() {
        try {
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/profile?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const data = await res.json();
                const user = data.user;
                const fullName = `${user.firstname} ${user.lastname}`;
                userEmailSpan.textContent = fullName;
                console.log('Updated navbar user display:', fullName);
            } else {
                // Fallback to email if profile fetch fails
                userEmailSpan.textContent = currentUserEmail;
            }
        } catch (err) {
            console.error('Error updating user profile display:', err);
            // Fallback to email on error
            userEmailSpan.textContent = currentUserEmail;
        }
    }

    async function loadProfile() {
        try {
            const res = await fetch('/api/profile', {
                credentials: 'include'
            });

            if (!res.ok) throw new Error('Failed to load profile');

            const data = await res.json();
            renderProfile(data);
        } catch (err) {
            console.error('Error loading profile:', err);
            postsContainer.innerHTML = '<p class="no-posts">Failed to load profile. Please try again.</p>';
        }
    }

    function renderProfile(data) {
        const { user, posts } = data;

        // Set user info
        profileName.textContent = `${user.firstname} ${user.lastname}`;
        profileEmail.textContent = user.email;
        postCount.textContent = `${posts.length} ${posts.length === 1 ? 'post' : 'posts'}`;

        // Render posts
        if (posts.length === 0) {
            postsContainer.innerHTML = '<p class="no-posts">You haven\'t posted anything yet.</p>';
            return;
        }

        postsContainer.innerHTML = '';

        posts.forEach(post => {
            let mediaContent = '';
            if (post.mediaUrl) {
                if (post.mediaType === 'image') {
                    mediaContent = `<div class="post-media"><img src="${post.mediaUrl}" alt="Post image"></div>`;
                } else if (post.mediaType === 'video') {
                    mediaContent = `
                        <div class="post-media">
                            <video controls>
                                <source src="${post.mediaUrl}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                }
            }

            const commentCount = post.comments ? post.comments.length : 0;

            const postElement = document.createElement('div');
            postElement.className = 'post';
            postElement.innerHTML = `
                <div class="post-header">
                    <div class="post-avatar"><i data-lucide="user"></i></div>
                    <div class="post-header-info">
                        <span class="post-user">${user.firstname} ${user.lastname}</span>
                        <span class="post-time">${formatDate(post.createdAt)}</span>
                    </div>
                    ${post.userEmail === currentUserEmail ? `
                        <div class="post-options">
                            <button class="btn-post-options" data-post-id="${post.id}">
                                <i data-lucide="more-horizontal"></i>
                            </button>
                            <div class="post-options-dropdown" id="dropdown-${post.id}">
                                <button class="dropdown-item btn-edit" data-post-id="${post.id}">
                                    <i data-lucide="edit"></i> Edit Post
                                </button>
                                <button class="dropdown-item btn-delete" data-post-id="${post.id}">
                                    <i data-lucide="trash-2"></i> Delete Post
                                </button>
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="post-content">${post.content}</div>
                ${mediaContent}
                <div class="post-actions">
                    <button class="btn-like ${post.likedBy.includes(user.email) ? 'liked' : ''}" data-post-id="${post.id}">
                        <i data-lucide="thumbs-up"></i>
                        <span class="like-count">${post.likes || 0}</span>
                    </button>
                    <button class="btn-view-comments" data-post-id="${post.id}">
                        <i data-lucide="message-circle"></i>
                        <span class="comment-count">${commentCount}</span> Comments
                    </button>
                </div>
                <div class="post-comments" id="comments-${post.id}" style="display: none;">
                    <div class="comments-container" id="comments-container-${post.id}">
                        <!-- Comments will be loaded here -->
                    </div>
                    <div class="comment-form">
                        <textarea class="comment-input" placeholder="Write a comment..." data-post-id="${post.id}"></textarea>
                        <button class="btn-post-comment" data-post-id="${post.id}">Post Comment</button>
                    </div>
                </div>
            `;
            postsContainer.appendChild(postElement);
        });

        // Initialize icons for new elements
        lucide.createIcons();

        // Add event listeners for post options dropdown
        document.querySelectorAll('.btn-post-options').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const postId = this.getAttribute('data-post-id');
                const dropdown = document.getElementById(`dropdown-${postId}`);

                // Close all other dropdowns
                document.querySelectorAll('.post-options-dropdown').forEach(d => {
                    if (d !== dropdown) {
                        d.classList.remove('show');
                    }
                });

                // Toggle current dropdown
                dropdown.classList.toggle('show');
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function() {
            document.querySelectorAll('.post-options-dropdown').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        });

        // Prevent dropdown from closing when clicking inside it
        document.querySelectorAll('.post-options-dropdown').forEach(dropdown => {
            dropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });

        // Add event listeners for like buttons
        document.querySelectorAll('.btn-like').forEach(btn => {
            btn.addEventListener('click', async function () {
                const postId = this.getAttribute('data-post-id');
                const likeCountElement = this.querySelector('.like-count');
                const iconElement = this.querySelector('i');
                const isLiked = this.classList.contains('liked');

                // Optimistic UI update
                this.classList.toggle('liked');
                const currentLikes = parseInt(likeCountElement.textContent);
                likeCountElement.textContent = isLiked ? currentLikes - 1 : currentLikes + 1;
                if (iconElement) {
                    iconElement.setAttribute('fill', isLiked ? 'none' : 'currentColor');
                }

                try {
                    const res = await fetch(`/api/posts/like?post_id=${postId}`, {
                        method: 'POST',
                        credentials: 'include'
                    });

                    if (!res.ok) {
                        // Revert UI if API call fails
                        this.classList.toggle('liked');
                        likeCountElement.textContent = isLiked ? currentLikes : currentLikes - 1;
                        if (iconElement) {
                            iconElement.setAttribute('fill', isLiked ? 'currentColor' : 'none');
                        }
                        throw new Error('Failed to like post');
                    }
                } catch (err) {
                    console.error('Error liking post:', err);
                }
            });
        });

        // Add event listeners for edit buttons
        document.querySelectorAll('.btn-edit').forEach(btn => {
            btn.addEventListener('click', function () {
                const postId = this.getAttribute('data-post-id');
                const postElement = this.closest('.post');
                const postContent = postElement.querySelector('.post-content').textContent;

                currentEditingPostId = postId;
                editPostContent.value = postContent;
                editModal.style.display = 'block';

                // Close the dropdown
                const dropdown = document.getElementById(`dropdown-${postId}`);
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            });
        });

        // Add event listeners for delete buttons
        document.querySelectorAll('.btn-delete').forEach(btn => {
            btn.addEventListener('click', async function () {
                const postId = this.getAttribute('data-post-id');

                // Close the dropdown
                const dropdown = document.getElementById(`dropdown-${postId}`);
                if (dropdown) {
                    dropdown.classList.remove('show');
                }

                if (confirm('Are you sure you want to delete this post?')) {
                    try {
                        const res = await fetch(`/api/posts?post_id=${postId}`, {
                            method: 'DELETE',
                            credentials: 'include'
                        });

                        if (res.ok) {
                            loadProfile(); // Refresh the posts list
                        } else {
                            throw new Error('Failed to delete post');
                        }
                    } catch (err) {
                        console.error('Error deleting post:', err);
                        alert('Failed to delete post. Please try again.');
                    }
                }
            });
        });

        // Add event listeners for comment buttons
        document.querySelectorAll('.btn-view-comments').forEach(btn => {
            btn.addEventListener('click', async function() {
                const postId = this.getAttribute('data-post-id');
                const commentsSection = document.getElementById(`comments-${postId}`);

                if (commentsSection.style.display === 'none') {
                    commentsSection.style.display = 'block';
                    await loadComments(postId);
                } else {
                    commentsSection.style.display = 'none';
                }
            });
        });

        // Add event listeners for post comment buttons
        document.querySelectorAll('.btn-post-comment').forEach(btn => {
            btn.addEventListener('click', async function() {
                const postId = this.getAttribute('data-post-id');
                const commentInput = document.querySelector(`.comment-input[data-post-id="${postId}"]`);
                const content = commentInput.value.trim();

                if (!content) {
                    alert('Please enter a comment');
                    return;
                }

                await postComment(postId, content, commentInput);
            });
        });

        // Add event listeners for comment input (Enter key)
        document.querySelectorAll('.comment-input').forEach(input => {
            input.addEventListener('keypress', async function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const postId = this.getAttribute('data-post-id');
                    const content = this.value.trim();

                    if (!content) return;

                    await postComment(postId, content, this);
                }
            });
        });
    }

    // Update post handler
    updatePostBtn.addEventListener('click', async function () {
        const newContent = editPostContent.value.trim();
        if (!newContent) {
            alert('Post content cannot be empty');
            return;
        }

        try {
            const res = await fetch(`/api/posts?post_id=${currentEditingPostId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({ content: newContent })
            });

            if (res.ok) {
                editModal.style.display = 'none';
                loadProfile(); // Refresh the posts list
            } else {
                throw new Error('Failed to update post');
            }
        } catch (err) {
            console.error('Error updating post:', err);
            alert('Failed to update post. Please try again.');
        }
    });

    async function loadComments(postId) {
        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                credentials: 'include'
            });

            if (res.ok) {
                const comments = await res.json();
                await renderComments(postId, comments);
            }
        } catch (err) {
            console.error('Error loading comments:', err);
        }
    }

    async function renderComments(postId, comments) {
        const commentsContainer = document.getElementById(`comments-container-${postId}`);

        if (comments.length === 0) {
            commentsContainer.innerHTML = '<p class="no-comments">No comments yet. Be the first to comment!</p>';
            return;
        }

        // Get all user profiles to update comment names for everyone
        let allUserProfiles = {};
        try {
            const timestamp = new Date().getTime();
            const usersRes = await fetch(`/api/users?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });
            if (usersRes.ok) {
                const users = await usersRes.json();
                users.forEach(user => {
                    allUserProfiles[user.email] = user;
                });
            }
        } catch (err) {
            console.error('Error fetching user profiles:', err);
        }

        // Sort comments by date (newest first, like Facebook)
        comments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        commentsContainer.innerHTML = comments.map(comment => {
            // Use updated username for ALL users' comments
            let displayName = comment.userName || 'Anonymous';
            const userProfile = allUserProfiles[comment.userEmail];
            if (userProfile) {
                displayName = `${userProfile.firstname} ${userProfile.lastname}`;
            }

            return `
                <div class="comment" data-comment-id="${comment.id}">
                    <div class="comment-header">
                        <div class="comment-avatar"><i data-lucide="user"></i></div>
                        <div>
                            <span class="comment-user">${displayName}</span>
                            <span class="comment-time">${formatDate(comment.createdAt)}</span>
                        </div>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                </div>
            `;
        }).join('');

        lucide.createIcons();
    }

    async function postComment(postId, content, inputElement) {
        // Get current user info for optimistic update
        let currentUser = '';
        let currentUserName = 'You';
        try {
            const res = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (res.ok) {
                const data = await res.json();
                currentUser = data.email;

                // Get current user's profile for updated name
                const timestamp = new Date().getTime();
                const profileRes = await fetch(`/api/profile?_t=${timestamp}`, {
                    credentials: 'include',
                    cache: 'no-cache'
                });
                if (profileRes.ok) {
                    const profileData = await profileRes.json();
                    currentUserName = `${profileData.user.firstname} ${profileData.user.lastname}`;
                }
            }
        } catch (err) {
            console.error('Error fetching user info:', err);
        }

        // Create temporary comment for optimistic UI update
        const tempComment = {
            id: 'temp-' + Date.now(),
            content: content,
            userName: currentUserName,
            createdAt: new Date().toISOString(),
            userEmail: currentUser
        };

        // Add comment to UI immediately
        const commentsContainer = document.getElementById(`comments-container-${postId}`);
        const noCommentsMsg = commentsContainer.querySelector('.no-comments');
        if (noCommentsMsg) {
            noCommentsMsg.remove();
        }

        const commentElement = document.createElement('div');
        commentElement.className = 'comment';
        commentElement.setAttribute('data-comment-id', tempComment.id);
        commentElement.innerHTML = `
            <div class="comment-header">
                <div class="comment-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="comment-user">${tempComment.userName}</span>
                    <span class="comment-time">${formatDate(tempComment.createdAt)}</span>
                </div>
            </div>
            <div class="comment-content">${tempComment.content}</div>
        `;
        // Add new comment at the top (newest first)
        commentsContainer.insertBefore(commentElement, commentsContainer.firstChild);
        lucide.createIcons();

        // Update comment count
        const commentCountElement = document.querySelector(`.btn-view-comments[data-post-id="${postId}"] .comment-count`);
        if (commentCountElement) {
            const currentCount = parseInt(commentCountElement.textContent);
            commentCountElement.textContent = currentCount + 1;
        }

        // Clear input
        inputElement.value = '';

        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ content: content })
            });

            if (res.ok) {
                const newComment = await res.json();
                // Replace temporary comment with real comment
                commentElement.setAttribute('data-comment-id', newComment.id);
                commentElement.querySelector('.comment-user').textContent = newComment.userName;
                commentElement.querySelector('.comment-time').textContent = formatDate(newComment.createdAt);
            } else {
                throw new Error('Failed to post comment');
            }
        } catch (err) {
            console.error('Error posting comment:', err);
            // Remove the temporary comment on error
            commentElement.remove();

            // Revert comment count
            if (commentCountElement) {
                const currentCount = parseInt(commentCountElement.textContent);
                commentCountElement.textContent = currentCount - 1;
            }

            // Restore input value
            inputElement.value = content;
            alert('Failed to post comment. Please try again.');
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = Math.floor((now - date) / 1000);
        if (diff < 60) return 'just now';
        if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
        return `${Math.floor(diff / 86400)} days ago`;
    }
});
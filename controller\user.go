package controller

import (
	"context"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetUsers gets all users
func GetUsers(w http.ResponseWriter, r *http.Request) {
	// Get current user's email
	currentUserEmail := r.Context().Value("user_email").(string)

	// Find all users except current user
	cursor, err := model.UserCollection.Find(context.Background(),
		bson.M{"email": bson.M{"$ne": currentUserEmail}})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch users")
		return
	}
	defer cursor.Close(context.Background())

	var users []model.User
	if err = cursor.All(context.Background(), &users); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to decode users")
		return
	}

	// Remove passwords from response
	for i := range users {
		users[i].Password = ""
	}

	httpResp.RespondWithJSON(w, http.StatusOK, users)
}

// SearchUsers searches for users by name or email
func SearchUsers(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	if query == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "search query is required")
		return
	}

	// Get current user's email
	currentUserEmail := r.Context().Value("user_email").(string)

	// Create case-insensitive regex pattern for "starts with" search
	searchPattern := primitive.Regex{Pattern: "^" + query, Options: "i"}

	// Search only in firstname and lastname fields (not email)
	filter := bson.M{
		"email": bson.M{"$ne": currentUserEmail}, // Exclude current user
		"$or": []bson.M{
			{"firstname": searchPattern},
			{"lastname": searchPattern},
		},
	}

	cursor, err := model.UserCollection.Find(context.Background(), filter)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to search users")
		return
	}
	defer cursor.Close(context.Background())

	var users []model.User
	if err = cursor.All(context.Background(), &users); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to decode users")
		return
	}

	// Remove passwords from response
	for i := range users {
		users[i].Password = ""
	}

	httpResp.RespondWithJSON(w, http.StatusOK, users)
}

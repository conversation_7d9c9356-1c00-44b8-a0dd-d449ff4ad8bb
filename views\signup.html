<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Sign Up</title>
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/animations.css">
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body class="page-transition">
    <div class="auth-container">
        <h2>Sign Up</h2>
        <form id="signupForm">
            <div class="form-group">
                <label for="fname">First Name *</label>
                <input type="text" id="fname" required>
            </div>
            <div class="form-group">
                <label for="lname">Last Name</label>
                <input type="text" id="lname">
            </div>
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="pw1">Password *</label>
                <div class="password-input-container">
                    <input type="password" id="pw1" required>
                    <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                        <i data-lucide="eye"></i>
                    </button>
                </div>
                <div class="password-strength">
                    <div id="password-strength-meter" class="password-strength-meter"></div>
                </div>
                <div id="password-requirements" class="password-requirements">
                    <ul>
                        <li id="req-length"><span class="req-icon">✗</span> At least 8 characters</li>
                        <li id="req-uppercase"><span class="req-icon">✗</span> At least 1 uppercase letter</li>
                        <li id="req-number"><span class="req-icon">✗</span> At least 1 number</li>
                        <li id="req-special"><span class="req-icon">✗</span> At least 1 special character</li>
                    </ul>
                </div>
            </div>
            <div class="form-group">
                <label for="pw2">Confirm Password *</label>
                <input type="password" id="pw2" required>
            </div>
            <button type="submit">Submit</button>
        </form>
        <p>Already have an account? <a href="index.html">Login here</a></p>
    </div>
    <script src="js/signup.js"></script>
    <script>
        lucide.createIcons();
    </script>
</body>

</html>
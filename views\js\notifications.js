// Notification System
class NotificationManager {
    constructor() {
        this.notificationBtn = document.getElementById('notificationBtn');
        this.notificationDropdown = document.getElementById('notificationDropdown');
        this.notificationBadge = document.getElementById('notificationBadge');
        this.notificationList = document.getElementById('notificationList');
        this.markAllReadBtn = document.getElementById('markAllReadBtn');

        this.init();
    }

    init() {
        if (!this.notificationBtn) return;

        // Toggle dropdown
        this.notificationBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            this.closeDropdown();
        });

        // Prevent dropdown from closing when clicking inside
        this.notificationDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Mark all as read
        this.markAllReadBtn.addEventListener('click', () => {
            this.markAllAsRead();
        });

        // Load notifications and start polling
        this.loadNotifications();
        this.loadUnreadCount();
        this.startPolling();


    }

    toggleDropdown() {
        const isVisible = this.notificationDropdown.classList.contains('show');
        if (isVisible) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    openDropdown() {
        this.notificationDropdown.classList.add('show');
        this.loadNotifications();
    }

    closeDropdown() {
        this.notificationDropdown.classList.remove('show');
    }

    async loadNotifications() {
        try {
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/notifications?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const notifications = await res.json();
                await this.renderNotifications(notifications);
            }
        } catch (err) {
            console.error('Error loading notifications:', err);
        }
    }

    async fetchUserProfiles(emails) {
        const profiles = {};

        try {
            // Fetch all users to get current profile data
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/users?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const users = await res.json();
                // Create a map of email -> profile for quick lookup
                users.forEach(user => {
                    if (emails.includes(user.email)) {
                        profiles[user.email] = user;
                    }
                });
            }
        } catch (err) {
            console.error('Error fetching user profiles:', err);
        }

        return profiles;
    }

    async loadUnreadCount() {
        try {
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/notifications/count?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const data = await res.json();
                this.updateBadge(data.count);
            }
        } catch (err) {
            console.error('Error loading notification count:', err);
        }
    }

    updateBadge(count) {
        if (count > 0) {
            this.notificationBadge.textContent = count > 99 ? '99+' : count;
            this.notificationBadge.style.display = 'flex';
        } else {
            this.notificationBadge.style.display = 'none';
        }
    }

    async renderNotifications(notifications) {
        if (notifications.length === 0) {
            this.notificationList.innerHTML = '<div class="no-notifications">No notifications yet</div>';
            return;
        }

        // Get unique actor emails to fetch current user profiles
        const actorEmails = [...new Set(notifications.map(n => n.actorEmail))];
        const userProfiles = await this.fetchUserProfiles(actorEmails);

        this.notificationList.innerHTML = notifications.map(notification => {
            const timeAgo = this.formatTimeAgo(notification.createdAt);
            const unreadClass = notification.isRead ? '' : 'unread';

            // Use updated actor name if available
            const currentProfile = userProfiles[notification.actorEmail];
            const actorName = currentProfile
                ? `${currentProfile.firstname} ${currentProfile.lastname}`
                : notification.actorName;

            let notificationText = '';
            if (notification.type === 'like') {
                notificationText = `<strong>${actorName}</strong> liked your post`;
            } else if (notification.type === 'comment') {
                notificationText = `<strong>${actorName}</strong> commented on your post`;
            }

            return `
                <div class="notification-item ${unreadClass}" data-notification-id="${notification.id}" data-post-id="${notification.postId}">
                    <div class="notification-avatar">
                        <i data-lucide="${notification.type === 'like' ? 'heart' : 'message-circle'}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">${notificationText}</div>
                        <div class="notification-time">${timeAgo}</div>
                        ${notification.postContent ? `<div class="notification-post-preview">"${notification.postContent}"</div>` : ''}
                        ${notification.commentText ? `<div class="notification-post-preview">Comment: "${notification.commentText}"</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        // Add click handlers for notifications
        this.notificationList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', () => {
                const notificationId = item.getAttribute('data-notification-id');
                const postId = item.getAttribute('data-post-id');
                this.handleNotificationClick(notificationId, postId);
            });
        });

        lucide.createIcons();
    }

    async handleNotificationClick(notificationId, postId) {
        // Mark notification as read
        try {
            await fetch(`/api/notifications/read?notification_id=${notificationId}`, {
                method: 'POST',
                credentials: 'include'
            });
        } catch (err) {
            console.error('Error marking notification as read:', err);
        }

        // Navigate to the post (you can customize this based on your routing)
        // For now, we'll just close the dropdown and refresh the page if we're not on home
        this.closeDropdown();
        
        if (window.location.pathname !== '/home.html') {
            window.location.href = 'home.html';
        } else {
            // Refresh notifications
            this.loadNotifications();
            this.loadUnreadCount();
        }
    }

    async markAllAsRead() {
        try {
            const res = await fetch('/api/notifications/read-all', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                this.loadNotifications();
                this.loadUnreadCount();
            }
        } catch (err) {
            console.error('Error marking all notifications as read:', err);
        }
    }

    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = Math.floor((now - date) / 1000);
        
        if (diff < 60) return 'just now';
        if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`;
        if (diff < 604800) return `${Math.floor(diff / 86400)}d ago`;
        return `${Math.floor(diff / 604800)}w ago`;
    }

    startPolling() {
        // Poll for new notifications every 30 seconds
        setInterval(() => {
            this.loadUnreadCount();
        }, 30000);
    }

    // Global method to refresh notifications
    refreshNotifications() {
        this.loadNotifications();
        this.loadUnreadCount();
    }
}

// Initialize notification manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();

    // Global function to refresh notifications
    window.refreshNotifications = function() {
        if (window.notificationManager) {
            window.notificationManager.refreshNotifications();
        }
    };
});

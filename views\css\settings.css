.settings-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.settings-container h1 {
    margin-bottom: 2rem;
    color: #333;
}

.settings-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.settings-section h2 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.password-input-container {
    position: relative;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.toggle-password:hover {
    color: #4a6cf7;
}

.toggle-password i {
    width: 18px;
    height: 18px;
}

.password-requirements {
    margin-top: 8px;
}

.password-requirements ul {
    list-style: none;
    padding-left: 5px;
    margin-top: 5px;
}

.password-requirements li {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
}

.req-icon {
    display: inline-block;
    width: 16px;
    text-align: center;
    margin-right: 5px;
    font-size: 10px;
}

.password-requirements li.valid {
    color: #00C851;
}

.password-requirements li.valid .req-icon {
    content: "✓";
    color: #00C851;
}

.alert-error {
    background-color: #ff4444;
    color: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in;
}

.alert-success {
    background-color: #00C851;
    color: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in;
}

.field-error {
    color: #ff4444;
    font-size: 12px;
    margin-top: 5px;
    animation: fadeIn 0.3s ease-in;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@media (max-width: 768px) {
    .settings-container {
        padding: 0 0.5rem;
    }
}
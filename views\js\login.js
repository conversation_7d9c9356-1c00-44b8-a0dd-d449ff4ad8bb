// DOM Elements
const loginForm = document.getElementById('loginForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const submitBtn = document.querySelector('button[type="submit"]');

// Track if email error has been shown
let emailErrorShown = false;

// Toggle password visibility
function togglePasswordVisibility(input, button) {
    const isPassword = input.type === 'password';
    input.type = isPassword ? 'text' : 'password';
    const icon = button.querySelector('i');
    icon.setAttribute('data-lucide', isPassword ? 'eye-off' : 'eye');
    lucide.createIcons();
}

// Show error message with smooth animation
function showError(message) {
    const container = document.querySelector('.auth-container');
    const existingError = container.querySelector('.alert-error');
    
    if (existingError) {
        existingError.style.animation = 'fadeOut 0.3s ease-out';
        existingError.addEventListener('animationend', () => {
            existingError.remove();
        });
    }

    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert-error';
    errorDiv.textContent = message;
    errorDiv.style.animation = 'fadeIn 0.3s ease-in';
    
    container.insertBefore(errorDiv, container.firstChild);

    setTimeout(() => {
        errorDiv.style.animation = 'fadeOut 0.3s ease-out';
        errorDiv.addEventListener('animationend', () => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        });
    }, 5000);
}

// Show success message with smooth animation
function showSuccess(message) {
    const container = document.querySelector('.auth-container');
    const existingAlert = container.querySelector('.alert-success');
    
    if (existingAlert) {
        existingAlert.style.animation = 'fadeOut 0.3s ease-out';
        existingAlert.addEventListener('animationend', () => {
            existingAlert.remove();
        });
    }

    const successDiv = document.createElement('div');
    successDiv.className = 'alert-success';
    successDiv.textContent = message;
    successDiv.style.animation = 'fadeIn 0.3s ease-in';
    
    container.insertBefore(successDiv, container.firstChild);
}

// Show field-specific error
function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    const existingError = field.nextElementSibling;
    if (existingError && existingError.classList.contains('field-error')) {
        existingError.style.animation = 'fadeOut 0.2s ease-out';
        existingError.addEventListener('animationend', () => {
            existingError.remove();
        });
    }

    if (message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.animation = 'fadeIn 0.2s ease-in';
        field.parentNode.insertBefore(errorElement, field.nextSibling);
        field.classList.add('error');
        if (fieldId === 'email') emailErrorShown = true;
    } else {
        field.classList.remove('error');
        if (fieldId === 'email') emailErrorShown = false;
    }
}

// Clear all field errors with animation
function clearFieldErrors() {
    document.querySelectorAll('.field-error').forEach(el => {
        el.style.animation = 'fadeOut 0.2s ease-out';
        el.addEventListener('animationend', () => {
            el.remove();
        });
    });
    document.querySelectorAll('.form-group input').forEach(input => {
        input.classList.remove('error');
    });
    emailErrorShown = false;
}

// Reset button to default state with animation
function resetButtonState() {
    submitBtn.disabled = false;
    submitBtn.classList.remove('btn-loading');
    submitBtn.innerHTML = 'Submit';
}

// Show button loading state
function showButtonLoading() {
    submitBtn.disabled = true;
    submitBtn.classList.add('btn-loading');
    submitBtn.textContent = 'Logging in...';
}

// Add shake animation to form elements
function addShakeAnimation(element) {
    element.classList.add('shake');
    setTimeout(() => {
        element.classList.remove('shake');
    }, 500);
}

// Validate email format
function validateEmail(email) {
    const re = /^\S+@\S+\.\S+$/;
    return re.test(email);
}

// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();

    if (submitBtn.disabled) {
        return;
    }

    const email = emailInput.value.trim();
    const password = passwordInput.value;
    
    clearFieldErrors();

    // Validation
    let isValid = true;

    if (!email) {
        showFieldError('email', 'This field is required');
        isValid = false;
    } else if (!validateEmail(email)) {
        showFieldError('email', 'Please enter a valid email');
        isValid = false;
    }

    if (!password) {
        showFieldError('password', 'This field is required');
        isValid = false;
    }

    if (!isValid) {
        // Add shake animation to form
        addShakeAnimation(loginForm);
        return;
    }

    // Show loading state
    showButtonLoading();

    try {
        // First check if email exists
        const emailCheck = await fetch(`/check-email?email=${encodeURIComponent(email)}`);
        
        if (!emailCheck.ok) {
            showFieldError('email', 'Account does not exist');
            addShakeAnimation(emailInput);
            passwordInput.value = '';
            resetButtonState();
            return;
        }

        // If email exists, try login
        const response = await fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
            credentials: 'include'
        });

        if (!response.ok) {
            showFieldError('password', 'Incorrect password');
            addShakeAnimation(passwordInput);
            passwordInput.value = '';
            resetButtonState();
            return;
        }

        const data = await response.json();
        showSuccess('Login successful! Redirecting...');
        document.querySelector('.auth-container').classList.add('disabled');

        // Add success animation to button
        submitBtn.classList.add('bounce-in');

        setTimeout(() => {
            window.location.href = data.redirect || '/home.html';
        }, 1500);
    } catch (error) {
        showError(error.message);
        resetButtonState();
    }
}

// Reset form to initial state
function resetFormState() {
    // Reset button state
    resetButtonState();

    // Clear all inputs
    if (emailInput) emailInput.value = '';
    if (passwordInput) passwordInput.value = '';

    // Clear all errors
    clearFieldErrors();

    // Remove any success/error messages
    const container = document.querySelector('.auth-container');
    const existingAlerts = container.querySelectorAll('.alert-success, .alert-error');
    existingAlerts.forEach(alert => alert.remove());

    // Remove disabled state from container
    container.classList.remove('disabled');

    // Reset form classes
    if (loginForm) {
        loginForm.classList.remove('shake');
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Reset form state on page load
    resetFormState();

    // Password toggle
    document.querySelectorAll('.toggle-password').forEach(button => {
        const input = button.parentElement.querySelector('input');
        button.addEventListener('click', () => {
            togglePasswordVisibility(input, button);
        });
    });

    // Form submission
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Clear errors when user types (only for password)
    passwordInput.addEventListener('input', function() {
        if (this.value.trim() !== '') {
            showFieldError('password', '');
        }
    });

    emailInput.addEventListener('input', function() {
        if (emailErrorShown) {
            showFieldError('email', '');
        }
    });
});

// Reset form when page becomes visible (e.g., when user navigates back)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        resetFormState();
    }
});

// Reset form when page is focused (additional safety)
window.addEventListener('focus', function() {
    resetFormState();
});
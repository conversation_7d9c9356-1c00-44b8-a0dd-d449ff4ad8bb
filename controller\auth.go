// controller/auth.go
package controller

import (
	"context"
	"encoding/json"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

func Signup(w http.ResponseWriter, r *http.Request) {
	var user model.User
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&user); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "invalid json body")
		return
	}
	defer r.Body.Close()

	if !model.IsEmailValid(user.Email) {
		httpResp.RespondWithError(w, http.StatusBadRequest, "invalid email format")
		return
	}

	if !model.IsPasswordStrong(user.Password) {
		httpResp.RespondWithError(w, http.StatusBadRequest,
			"password must be at least 8 characters long with uppercase, lowercase, number, and special character")
		return
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "could not hash password")
		return
	}
	user.Password = string(hashedPassword)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var existingUser model.User
	err = model.UserCollection.FindOne(ctx, bson.M{"email": user.Email}).Decode(&existingUser)
	if err == nil {
		httpResp.RespondWithError(w, http.StatusConflict, "email already exists")
		return
	} else if err != mongo.ErrNoDocuments {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "server error")
		return
	}

	_, err = model.UserCollection.InsertOne(ctx, user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, err.Error())
		return
	}

	httpResp.RespondWithJSON(w, http.StatusCreated, map[string]string{"status": "user created"})
}

func Login(w http.ResponseWriter, r *http.Request) {
	var credentials struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&credentials); err != nil {
		httpResp.RespondWithError(w, http.StatusBadRequest, "invalid json body")
		return
	}
	defer r.Body.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var user model.User
	err := model.UserCollection.FindOne(ctx, bson.M{"email": credentials.Email}).Decode(&user)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "invalid email or password")
		return
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(credentials.Password))
	if err != nil {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "invalid email or password")
		return
	}

	accessToken, refreshToken, err := model.GenerateJWTToken(user.Email)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "could not generate token")
		return
	}

	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    accessToken,
		Expires:  time.Now().Add(15 * time.Minute),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
	})

	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    refreshToken,
		Expires:  time.Now().Add(7 * 24 * time.Hour),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
	})

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{
		"status": "login successful",
	})
}

func Logout(w http.ResponseWriter, r *http.Request) {
	accessCookie, err := r.Cookie("access_token")
	if err == nil {
		model.InvalidateToken(accessCookie.Value)
	}

	refreshCookie, err := r.Cookie("refresh_token")
	if err == nil {
		model.InvalidateToken(refreshCookie.Value)
	}

	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    "",
		Expires:  time.Now().Add(-1 * time.Hour),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
	})

	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    "",
		Expires:  time.Now().Add(-1 * time.Hour),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
	})

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "logged out"})
}

func Refresh(w http.ResponseWriter, r *http.Request) {
	refreshCookie, err := r.Cookie("refresh_token")
	if err != nil {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "refresh token missing")
		return
	}

	claims, err := model.ValidateToken(refreshCookie.Value)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "invalid refresh token")
		return
	}

	if model.IsTokenBlacklisted(refreshCookie.Value) {
		httpResp.RespondWithError(w, http.StatusUnauthorized, "refresh token revoked")
		return
	}

	accessToken, refreshToken, err := model.GenerateJWTToken(claims.Email)
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "could not generate token")
		return
	}

	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    accessToken,
		Expires:  time.Now().Add(15 * time.Minute),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
	})

	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    refreshToken,
		Expires:  time.Now().Add(7 * 24 * time.Hour),
		HttpOnly: true,
		Secure:   true,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
	})

	model.InvalidateToken(refreshCookie.Value)

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{
		"status": "token refreshed",
	})
}

func CheckEmail(w http.ResponseWriter, r *http.Request) {
	email := r.URL.Query().Get("email")
	if email == "" {
		httpResp.RespondWithError(w, http.StatusBadRequest, "email parameter is required")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var existingUser model.User
	err := model.UserCollection.FindOne(ctx, bson.M{"email": email}).Decode(&existingUser)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "email not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "server error")
		return
	}

	httpResp.RespondWithJSON(w, http.StatusOK, map[string]string{"status": "email exists"})
}

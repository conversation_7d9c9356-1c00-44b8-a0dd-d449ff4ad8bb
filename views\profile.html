<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - MyApp</title>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/profile.css">
    <link rel="stylesheet" href="css/general.css">
    <link rel="stylesheet" href="css/loading.css">
    <link rel="stylesheet" href="css/animations.css">
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body>

    <nav class="navbar">
        <a href="home.html" class="navbar-logo">MyApp</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="home.html" class="nav-link">Home</a></li>
            <li class="nav-item"><a href="users.html" class="nav-link">Users</a></li>
        </ul>
        <div class="navbar-center">
            <div class="navbar-search">
                <i data-lucide="search" class="search-icon"></i>
                <input type="text" id="navbarSearchInput" class="search-input" placeholder="Search for people...">
                <div id="navbarSearchDropdown" class="search-dropdown">
                    <div id="navbarSearchResults"></div>
                </div>
            </div>
        </div>
        <div class="navbar-right">
            <div class="notification-menu">
                <button id="notificationBtn" class="notification-btn">
                    <i data-lucide="bell"></i>
                    <span id="notificationBadge" class="notification-badge" style="display: none;">0</span>
                </button>
                <div id="notificationDropdown" class="notification-dropdown">
                    <div class="notification-header">
                        <h3>Notifications</h3>
                        <button id="markAllReadBtn" class="mark-all-read-btn">Mark all as read</button>
                    </div>
                    <div id="notificationList" class="notification-list">
                        <!-- Notifications will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="user-menu">
                <button id="userBtn" class="user-btn">
                    <span id="userEmail"></span>
                    <i data-lucide="chevron-down"></i>
                </button>
                <div id="dropdownMenu" class="dropdown-menu">
                    <a href="profile.html" class="dropdown-link">Profile</a>
                    <a href="settings.html" class="dropdown-link">Settings</a>
                    <a href="#" id="logoutBtn" class="dropdown-link">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <main class="profile-container">
        <div class="profile-header">
            <div class="profile-avatar">
                <i data-lucide="user"></i>
            </div>
            <div class="profile-info">
                <h1 id="profileName"></h1>
                <p id="profileEmail"></p>
                <p id="postCount"></p>
            </div>
        </div>

        <div class="profile-content">
            <h2>My Posts</h2>
            <div class="posts-container" id="postsContainer">
                <!-- Posts will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Edit Post Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h3>Edit Post</h3>
            <textarea id="editPostContent" class="form-control"></textarea>
            <div id="editMediaPreview" class="media-preview"></div>
            <div class="modal-actions">
                <button id="updatePostBtn" class="btn-primary">Update</button>
                <button id="cancelEditBtn" class="btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; GCIT NEWS</p>
    </footer>

    <script src="js/lazy-loading.js"></script>
    <script src="js/infinite-scroll.js"></script>
    <script src="js/profile.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/navbar-search.js"></script>
    <script>
        lucide.createIcons();
    </script>
</body>

</html>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();

    const userBtn = document.getElementById('userBtn');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const logoutBtn = document.getElementById('logoutBtn');
    const userEmailSpan = document.getElementById('userEmail');
    const profileForm = document.getElementById('profileForm');
    const passwordForm = document.getElementById('passwordForm');
    const firstNameInput = document.getElementById('firstName');
    const lastNameInput = document.getElementById('lastName');
    const emailInput = document.getElementById('email');
    const currentPasswordInput = document.getElementById('currentPassword');
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');

    let currentUser = null;

    // Toggle dropdown
    userBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
    dropdownMenu.addEventListener('click', (e) => e.stopPropagation());

    logoutBtn.addEventListener('click', async function(e) {
        e.preventDefault();
        try {
            const res = await fetch('/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                // Use replace to avoid back button issues and full URL to bypass service worker
                window.location.replace(window.location.origin + '/index.html');
            } else {
                throw new Error('Logout failed');
            }
        } catch (err) {
            alert('Logout failed. Try again.');
        }
    });

    // Toggle password visibility
    document.querySelectorAll('.toggle-password').forEach(button => {
        const input = button.parentElement.querySelector('input');
        button.addEventListener('click', () => {
            togglePasswordVisibility(input, button);
        });
    });

    function togglePasswordVisibility(input, button) {
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';
        const icon = button.querySelector('i');
        icon.setAttribute('data-lucide', isPassword ? 'eye-off' : 'eye');
        lucide.createIcons();
    }

    // Check password strength
    function checkPasswordStrength(password) {
        let strength = 0;

        if (password.length >= 8) strength++;
        if (password.length >= 12) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        return strength;
    }

    // Validate password requirements
    function validatePasswordRequirements(password) {
        const hasLength = password.length >= 8;
        const hasUppercase = /[A-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecial = /[^A-Za-z0-9]/.test(password);

        document.getElementById('req-length').classList.toggle('valid', hasLength);
        document.getElementById('req-uppercase').classList.toggle('valid', hasUppercase);
        document.getElementById('req-number').classList.toggle('valid', hasNumber);
        document.getElementById('req-special').classList.toggle('valid', hasSpecial);

        const reqLengthIcon = document.querySelector('#req-length .req-icon');
        const reqUppercaseIcon = document.querySelector('#req-uppercase .req-icon');
        const reqNumberIcon = document.querySelector('#req-number .req-icon');
        const reqSpecialIcon = document.querySelector('#req-special .req-icon');

        reqLengthIcon.textContent = hasLength ? '✓' : '✗';
        reqUppercaseIcon.textContent = hasUppercase ? '✓' : '✗';
        reqNumberIcon.textContent = hasNumber ? '✓' : '✗';
        reqSpecialIcon.textContent = hasSpecial ? '✓' : '✗';

        return hasLength && hasUppercase && hasNumber && hasSpecial;
    }

    // Show error message
    function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert-error';
        errorDiv.textContent = message;

        const container = document.querySelector('.settings-container');
        const existingError = container.querySelector('.alert-error');
        if (existingError) {
            container.removeChild(existingError);
        }

        container.insertBefore(errorDiv, container.firstChild);
        setTimeout(() => {
            errorDiv.style.opacity = '0';
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    container.removeChild(errorDiv);
                }
            }, 500);
        }, 5000);
    }

    // Show success message
    function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'alert-success';
        successDiv.textContent = message;

        const container = document.querySelector('.settings-container');
        const existingAlert = container.querySelector('.alert-success');
        if (existingAlert) {
            container.removeChild(existingAlert);
        }

        container.insertBefore(successDiv, container.firstChild);
        setTimeout(() => {
            successDiv.style.opacity = '0';
            setTimeout(() => {
                if (successDiv.parentNode) {
                    container.removeChild(successDiv);
                }
            }, 500);
        }, 5000);
    }

    // Show field-specific error
    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        if (!field) return;

        const existingError = field.nextElementSibling;
        if (existingError && existingError.classList.contains('field-error')) {
            existingError.remove();
        }

        if (message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            field.parentNode.insertBefore(errorElement, field.nextSibling);
            field.classList.add('error');
        } else {
            field.classList.remove('error');
        }
    }

    // Clear all field errors
    function clearFieldErrors() {
        document.querySelectorAll('.field-error').forEach(el => el.remove());
        document.querySelectorAll('.form-control').forEach(input => {
            input.classList.remove('error');
        });
    }

    // Check auth status and load user data
    async function checkAuthStatus() {
        try {
            const res = await fetch('/api/check-auth', {
                method: 'GET',
                credentials: 'include'
            });

            if (!res.ok) {
                window.location.href = 'index.html';
                return;
            }

            const data = await res.json();
            userEmailSpan.textContent = data.email;
            currentUser = data;
            loadUserData(data.email);
        } catch (err) {
            console.error('Auth check error:', err);
            window.location.href = 'index.html';
        }
    }

    // Load user data
    async function loadUserData(email) {
        try {
            const res = await fetch('/api/profile', {
                credentials: 'include'
            });

            if (!res.ok) throw new Error('Failed to load profile');

            const data = await res.json();
            populateForm(data.user);
        } catch (err) {
            console.error('Error loading user data:', err);
            showError('Failed to load user data. Please try again.');
        }
    }

    // Populate form with user data
    function populateForm(user) {
        firstNameInput.value = user.firstname || '';
        lastNameInput.value = user.lastname || '';
        emailInput.value = user.email || '';
    }

    // Handle profile form submission
    profileForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();

        if (!firstName) {
            showFieldError('firstName', 'First name is required');
            return;
        }

        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner"></span> Updating...';

        try {
            const res = await fetch('/api/profile/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    firstname: firstName,
                    lastname: lastName
                })
            });

            if (!res.ok) {
                const errorData = await res.json();
                throw new Error(errorData.error || 'Failed to update profile');
            }

            showSuccess('Profile updated successfully!');
        } catch (err) {
            showError(err.message);
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Update Profile';
        }
    });

    // Handle password form submission
    passwordForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const currentPassword = currentPasswordInput.value;
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        clearFieldErrors();

        // Validate inputs
        let isValid = true;

        if (!currentPassword) {
            showFieldError('currentPassword', 'Current password is required');
            isValid = false;
        }

        if (!newPassword) {
            showFieldError('newPassword', 'New password is required');
            isValid = false;
        } else if (!validatePasswordRequirements(newPassword)) {
            showFieldError('newPassword', 'Password does not meet requirements');
            isValid = false;
        }

        if (!confirmPassword) {
            showFieldError('confirmPassword', 'Please confirm your new password');
            isValid = false;
        } else if (newPassword !== confirmPassword) {
            showFieldError('confirmPassword', 'Passwords do not match');
            isValid = false;
        }

        if (!isValid) return;

        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner"></span> Updating...';

        try {
            const res = await fetch('/api/profile/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    currentPassword,
                    newPassword
                })
            });

            if (!res.ok) {
                const errorData = await res.json();
                if (errorData.error === "invalid current password") {
                    showFieldError('currentPassword', 'Incorrect current password');
                } else {
                    throw new Error(errorData.error || 'Failed to change password');
                }
                return;
            }

            showSuccess('Password changed successfully!');
            currentPasswordInput.value = '';
            newPasswordInput.value = '';
            confirmPasswordInput.value = '';
            validatePasswordRequirements('');
        } catch (err) {
            showError(err.message);
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Change Password';
        }
    });

    // Real-time password validation
    newPasswordInput.addEventListener('input', function() {
        validatePasswordRequirements(this.value);
        if (validatePasswordRequirements(this.value)) {
            showFieldError('newPassword', '');
        }
    });

    confirmPasswordInput.addEventListener('input', function() {
        if (this.value === newPasswordInput.value) {
            showFieldError('confirmPassword', '');
        }
    });

    // Initialize
    checkAuthStatus();
});
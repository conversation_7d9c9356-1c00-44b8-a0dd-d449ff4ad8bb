package model

import (
	"time"
)

type Post struct {
	ID        string    `json:"id" bson:"_id"`
	Content   string    `json:"content" bson:"content"`
	UserEmail string    `json:"userEmail" bson:"userEmail"`
	UserName  string    `json:"userName" bson:"userName"`
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	Likes     int       `json:"likes" bson:"likes"`
	LikedBy   []string  `json:"likedBy" bson:"likedBy"`
	MediaURL  string    `json:"mediaUrl,omitempty" bson:"mediaUrl,omitempty"`
	MediaType string    `json:"mediaType,omitempty" bson:"mediaType,omitempty"`
	Comments  []Comment `json:"comments,omitempty" bson:"comments,omitempty"`
}

type Comment struct {
	ID        string    `json:"id" bson:"id"`
	Content   string    `json:"content" bson:"content"`
	UserEmail string    `json:"userEmail" bson:"userEmail"`
	UserName  string    `json:"userName" bson:"userName"`
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	Likes     int       `json:"likes" bson:"likes"`
	LikedBy   []string  `json:"likedBy" bson:"likedBy"`
	Replies   []Reply   `json:"replies,omitempty" bson:"replies,omitempty"`
}

type Reply struct {
	ID        string    `json:"id" bson:"id"`
	Content   string    `json:"content" bson:"content"`
	UserEmail string    `json:"userEmail" bson:"userEmail"`
	UserName  string    `json:"userName" bson:"userName"`
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	Likes     int       `json:"likes" bson:"likes"`
	LikedBy   []string  `json:"likedBy" bson:"likedBy"`
}

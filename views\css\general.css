* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html {
    overflow-x: hidden;
}

body {
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    padding: 2rem;
}

.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
}

/* Buttons */
.btn-primary {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover {
    background: #3a5bd9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 108, 247, 0.3);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-like {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s;
}

.btn-like:hover {
    background-color: #f0f0f0;
}

.btn-like i {
    width: 16px;
    height: 16px;
    transition: fill 0.2s;
}

.btn-like.liked,
.btn-like.liked:hover {
    color: #4a6cf7;
}

.btn-like.liked i {
    fill: #4a6cf7;
}

.like-count {
    margin-left: 3px;
}

.btn-media {
    background: #f0f0f0;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #555;
    transition: all 0.2s;
}

.btn-media:hover {
    background: #e0e0e0;
}

.btn-media i {
    width: 18px;
    height: 18px;
}

/* Cards */
.card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

/* Posts */
.post {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.post:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
}

.post-header-info {
    flex: 1;
}

.post-avatar {
    width: 40px;
    height: 40px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.post-avatar i {
    width: 20px;
    height: 20px;
    color: #666;
}

.post-user {
    font-weight: 600;
    display: block;
}

.post-time {
    font-size: 0.8rem;
    color: #666;
}

.post-content {
    margin-bottom: 1rem;
    white-space: pre-line;
}

.post-media {
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.post-media img,
.post-media video {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.post-media img.lazy-loading,
.post-media video.lazy-loading {
    opacity: 0.7;
    background: #f0f0f0;
}

.post-media img.fade-in,
.post-media video.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.post-media img,
.post-media video {
    max-width: 100%;
    max-height: 500px;
    border-radius: 8px;
    display: block;
}

/* Post Options Dropdown */
.post-options {
    position: relative;
    margin-left: auto;
}

.btn-post-options {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.btn-post-options:hover {
    background-color: #f0f0f0;
}

.btn-post-options i {
    width: 20px;
    height: 20px;
}

.post-options-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 150px;
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.post-options-dropdown.show {
    display: block;
}

.dropdown-item {
    width: 100%;
    background: none;
    border: none;
    padding: 12px 16px;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    font-size: 14px;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    width: 16px;
    height: 16px;
}

.dropdown-item.btn-delete {
    color: #dc3545;
}

.dropdown-item.btn-delete:hover {
    background-color: #f8d7da;
}

.post-actions {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    display: flex;
    gap: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #4a6cf7;
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Utility classes */
.no-content {
    text-align: center;
    color: #666;
    padding: 2rem;
    font-style: italic;
}

/* Comments */
.post-comments {
    margin-top: 1rem;
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.btn-view-comments {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.btn-view-comments:hover {
    background-color: #f0f0f0;
}

.btn-view-comments i {
    width: 16px;
    height: 16px;
}

.comments-container {
    margin-top: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.comment {
    padding: 0.75rem;
    background: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-avatar {
    width: 30px;
    height: 30px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.comment-avatar i {
    width: 16px;
    height: 16px;
    color: #666;
}

.comment-user {
    font-weight: 600;
    font-size: 0.9rem;
    display: block;
}

.comment-time {
    font-size: 0.7rem;
    color: #666;
}

.comment-content {
    font-size: 0.9rem;
    margin-left: 2.25rem;
}

.comment-actions {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    margin-left: 2.25rem;
}

.btn-like-comment, .btn-reply-comment {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    font-size: 0.8rem;
}

.btn-like-comment:hover, .btn-reply-comment:hover {
    background-color: #f0f0f0;
}

.btn-like-comment.liked {
    color: #dc3545;
}

.btn-like-comment i, .btn-reply-comment i {
    width: 14px;
    height: 14px;
}

.comment-replies {
    margin-left: 2.25rem;
    margin-top: 0.5rem;
    border-left: 2px solid #eee;
    padding-left: 1rem;
}

.reply {
    padding: 0.5rem;
    background: #f5f5f5;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.reply-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.reply-avatar {
    width: 24px;
    height: 24px;
    background: #e0e0e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}

.reply-avatar i {
    width: 12px;
    height: 12px;
    color: #666;
}

.reply-user {
    font-weight: 600;
    font-size: 0.8rem;
    display: block;
}

.reply-time {
    font-size: 0.7rem;
    color: #666;
}

.reply-content {
    font-size: 0.8rem;
    margin-left: 1.75rem;
}

.reply-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 0.25rem;
    margin-left: 1.75rem;
}

.btn-like-reply {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    font-size: 0.75rem;
}

.btn-like-reply:hover {
    background-color: #e8e8e8;
}

.btn-like-reply.liked {
    color: #dc3545;
}

.btn-like-reply i {
    width: 12px;
    height: 12px;
}

.reply-form {
    margin-top: 0.5rem;
    margin-left: 2.25rem;
    display: none;
}

.reply-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    resize: vertical;
    min-height: 40px;
}

.btn-post-reply {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.btn-post-reply:hover {
    background: #3a5bd9;
}

.btn-cancel-reply {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-cancel-reply:hover {
    background: #5a6268;
}

.replies-section {
    margin-left: 2.25rem;
    margin-top: 0.5rem;
}

.btn-toggle-replies {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.btn-toggle-replies:hover {
    background-color: #f0f0f0;
}

.btn-toggle-replies i {
    width: 12px;
    height: 12px;
    transition: transform 0.2s;
}

.btn-toggle-replies.collapsed i {
    transform: rotate(-90deg);
}

.comment-form {
    margin-top: 1rem;
}

.comment-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    resize: vertical;
    min-height: 60px;
}

.btn-post-comment {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-post-comment:hover {
    background: #3a5bd9;
}

.no-comments {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}
/* Footer */
.footer {
    background-color: #e4e5e6;
    color: rgb(0, 0, 0);
    text-align: center;
    padding: 1.5rem;
    margin-top: auto;
}



/* Responsive */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 0 1rem;
    }

    .main-content {
        padding: 1rem;
    }
}
// Service Worker Cleanup Utility
// This script ensures any lingering service workers are unregistered
// to prevent fetch interception issues

(function() {
    'use strict';

    // Function to unregister all service workers
    async function unregisterServiceWorkers() {
        if ('serviceWorker' in navigator) {
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                
                for (let registration of registrations) {
                    console.log('Unregistering service worker:', registration.scope);
                    await registration.unregister();
                }
                
                if (registrations.length > 0) {
                    console.log(`Unregistered ${registrations.length} service worker(s)`);
                }
            } catch (error) {
                console.warn('Error unregistering service workers:', error);
            }
        }
    }

    // Function to clear caches
    async function clearCaches() {
        if ('caches' in window) {
            try {
                const cacheNames = await caches.keys();
                
                for (let cacheName of cacheNames) {
                    console.log('Deleting cache:', cacheName);
                    await caches.delete(cacheName);
                }
                
                if (cacheNames.length > 0) {
                    console.log(`Cleared ${cacheNames.length} cache(s)`);
                }
            } catch (error) {
                console.warn('Error clearing caches:', error);
            }
        }
    }

    // Run cleanup when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            unregisterServiceWorkers();
            clearCaches();
        });
    } else {
        unregisterServiceWorkers();
        clearCaches();
    }

    // Expose cleanup functions globally for manual use
    window.cleanupServiceWorkers = unregisterServiceWorkers;
    window.clearAllCaches = clearCaches;
})();

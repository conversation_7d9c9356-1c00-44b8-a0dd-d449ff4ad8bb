document.addEventListener('DOMContentLoaded', function () {
    lucide.createIcons();

    const userBtn = document.getElementById('userBtn');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const logoutBtn = document.getElementById('logoutBtn');
    const userEmailSpan = document.getElementById('userEmail');
    const postBtn = document.getElementById('postBtn');
    const postContent = document.getElementById('postContent');
    const postsContainer = document.getElementById('postsContainer');
    const mediaInput = document.getElementById('mediaInput');
    const mediaPreview = document.getElementById('mediaPreview');
    const removeMediaBtn = document.getElementById('removeMediaBtn');
    const activityList = document.getElementById('activityList');
    const refreshActivityBtn = document.getElementById('refreshActivityBtn');

    // Cache current user email for activity personalization
    let currentUserEmailCache = null;
    let postsInfiniteScroll = null; // Infinite scroll instance
    let isInitialLoad = true; // Track if this is the first load

    // Toggle dropdown
    userBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
    dropdownMenu.addEventListener('click', (e) => e.stopPropagation());

    logoutBtn.addEventListener('click', async function (e) {
        e.preventDefault();
        try {
            const res = await fetch('/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                // Use replace to avoid back button issues and full URL to bypass service worker
                window.location.replace(window.location.origin + '/index.html');
            } else {
                throw new Error('Logout failed');
            }
        } catch (err) {
            alert('Logout failed. Try again.');
        }
    });

    checkAuthStatus();

    async function checkAuthStatus() {
        try {
            const res = await fetch('/api/check-auth', {
                method: 'GET',
                credentials: 'include'
            });

            if (!res.ok) return window.location.href = 'index.html';

            // Fetch user profile to show full name instead of email
            await updateUserProfile();
        } catch (err) {
            console.error('Auth check error:', err);
            window.location.href = 'index.html';
        }
    }

    // Update user profile display in navbar
    async function updateUserProfile() {
        try {
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/profile?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const data = await res.json();
                const user = data.user;
                const fullName = `${user.firstname} ${user.lastname}`;
                userEmailSpan.textContent = fullName;
                console.log('Updated navbar user display:', fullName);
            } else {
                // Fallback to email if profile fetch fails
                const authRes = await fetch('/api/check-auth', {
                    credentials: 'include'
                });
                if (authRes.ok) {
                    const authData = await authRes.json();
                    userEmailSpan.textContent = authData.email;
                }
            }
        } catch (err) {
            console.error('Error updating user profile display:', err);
            // Fallback to email on error
            try {
                const authRes = await fetch('/api/check-auth', {
                    credentials: 'include'
                });
                if (authRes.ok) {
                    const authData = await authRes.json();
                    userEmailSpan.textContent = authData.email;
                }
            } catch (fallbackErr) {
                console.error('Fallback error:', fallbackErr);
            }
        }
    }

    if (postBtn) {
        postBtn.addEventListener('click', async function (e) {
            e.preventDefault();
            const content = postContent.value.trim();

            if (!content && !mediaInput.files[0]) {
                postContent.classList.add('shake');
                setTimeout(() => postContent.classList.remove('shake'), 500);
                return alert('Please enter some content or add media');
            }

            showButtonLoading(postBtn);

            try {
                const formData = new FormData();
                formData.append('content', content);
                if (mediaInput.files[0]) {
                    formData.append('media', mediaInput.files[0]);
                }

                const res = await fetch('/api/posts', {
                    method: 'POST',
                    credentials: 'include',
                    body: formData
                });

                if (res.ok) {
                    postContent.value = '';
                    mediaInput.value = '';
                    mediaPreview.innerHTML = '';
                    mediaPreview.style.display = 'none';

                    // Reset infinite scroll and reload posts
                    if (postsInfiniteScroll) {
                        postsInfiniteScroll.reset();
                    }
                    loadPosts();

                    // Refresh activity after creating a post
                    if (activityList) {
                        loadRecentActivity();
                    }

                    // Show success animation
                    postBtn.classList.add('bounce-in');
                    setTimeout(() => postBtn.classList.remove('bounce-in'), 600);
                } else {
                    throw new Error('Post creation failed');
                }
            } catch (err) {
                postBtn.classList.add('shake');
                setTimeout(() => postBtn.classList.remove('shake'), 500);
                alert('Could not create post. Try again.');
            } finally {
                hideButtonLoading(postBtn);
            }
        });
    }

    if (mediaInput) {
        mediaInput.addEventListener('change', function() {
            const file = this.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                mediaPreview.style.display = 'block';
                
                if (file.type.startsWith('image/')) {
                    mediaPreview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview">
                        <button id="removeMediaBtn" class="remove-media-btn">
                            <i data-lucide="x"></i>
                        </button>
                    `;
                } else if (file.type.startsWith('video/')) {
                    mediaPreview.innerHTML = `
                        <video controls>
                            <source src="${e.target.result}" type="${file.type}">
                            Your browser does not support the video tag.
                        </video>
                        <button id="removeMediaBtn" class="remove-media-btn">
                            <i data-lucide="x"></i>
                        </button>
                    `;
                }
                
                lucide.createIcons();
                document.getElementById('removeMediaBtn').addEventListener('click', removeMedia);
            };
            reader.readAsDataURL(file);
        });
    }

    function removeMedia() {
        mediaInput.value = '';
        mediaPreview.innerHTML = '';
        mediaPreview.style.display = 'none';
    }

    // Set up event delegation for comment interactions
    function setupCommentEventDelegation() {
        // Use event delegation on the posts container
        if (postsContainer) {
            postsContainer.addEventListener('click', async function(e) {
                const target = e.target.closest('button');
                if (!target) return;

                // Handle comment like buttons
                if (target.classList.contains('btn-like-comment')) {
                    const postId = target.getAttribute('data-post-id');
                    const commentId = target.getAttribute('data-comment-id');
                    await likeComment(postId, commentId, target);
                }

                // Handle reply buttons (show/hide reply form)
                else if (target.classList.contains('btn-reply-comment')) {
                    const commentId = target.getAttribute('data-comment-id');
                    const replyForm = document.getElementById(`reply-form-${commentId}`);
                    if (replyForm) {
                        const isHidden = replyForm.style.display === 'none' || replyForm.style.display === '';
                        replyForm.style.display = isHidden ? 'block' : 'none';
                    }
                }

                // Handle post reply buttons
                else if (target.classList.contains('btn-post-reply')) {
                    const postId = target.getAttribute('data-post-id');
                    const commentId = target.getAttribute('data-comment-id');
                    const replyInput = document.querySelector(`.reply-input[data-comment-id="${commentId}"]`);
                    const content = replyInput.value.trim();

                    if (!content) {
                        alert('Please enter a reply');
                        return;
                    }

                    await postReply(postId, commentId, content, replyInput);
                }

                // Handle cancel reply buttons
                else if (target.classList.contains('btn-cancel-reply')) {
                    const commentId = target.getAttribute('data-comment-id');
                    const replyForm = document.getElementById(`reply-form-${commentId}`);
                    const replyInput = replyForm.querySelector('.reply-input');
                    replyInput.value = '';
                    replyForm.style.display = 'none';
                }

                // Handle reply like buttons
                else if (target.classList.contains('btn-like-reply')) {
                    const postId = target.getAttribute('data-post-id');
                    const commentId = target.getAttribute('data-comment-id');
                    const replyId = target.getAttribute('data-reply-id');
                    await likeReply(postId, commentId, replyId, target);
                }

                // Handle toggle replies buttons
                else if (target.classList.contains('btn-toggle-replies')) {
                    const commentId = target.getAttribute('data-comment-id');
                    const repliesContainer = document.getElementById(`replies-${commentId}`);

                    if (repliesContainer.style.display === 'none') {
                        // Show replies
                        repliesContainer.style.display = 'block';
                        target.classList.remove('collapsed');
                        const replyCount = repliesContainer.querySelectorAll('.reply').length;
                        target.innerHTML = `<i data-lucide="chevron-down"></i>Hide ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
                    } else {
                        // Hide replies
                        repliesContainer.style.display = 'none';
                        target.classList.add('collapsed');
                        const replyCount = repliesContainer.querySelectorAll('.reply').length;
                        target.innerHTML = `<i data-lucide="chevron-right"></i>Show ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
                    }
                    lucide.createIcons();
                }
            });
        }
    }

    // Initialize content loading
    if (postsContainer) {
        loadInitialPosts();
        // Set up event delegation for comment interactions
        setupCommentEventDelegation();
    }

    if (activityList) {
        loadRecentActivity();

        // Set up auto-refresh every 2 minutes
        setInterval(() => {
            loadRecentActivity();
        }, 120000); // 2 minutes
    }

    // Refresh activity button
    if (refreshActivityBtn) {
        refreshActivityBtn.addEventListener('click', function() {
            this.style.transform = 'rotate(360deg)';
            loadRecentActivity();
            setTimeout(() => {
                this.style.transform = '';
            }, 500);
        });
    }

    // Global function to refresh posts (can be called from other pages)
    window.refreshPosts = function() {
        if (postsContainer) {
            loadPosts();
        }
        // Also refresh activity when posts are refreshed
        if (activityList) {
            loadRecentActivity();
        }
    };

    // Global function to refresh user profile display
    window.refreshUserProfile = function() {
        updateUserProfile();
    };

    // Skeleton loading functions
    function showSkeletonPosts() {
        if (!postsContainer) return;

        const skeletonHTML = Array(3).fill(0).map(() => `
            <div class="skeleton-post">
                <div class="skeleton-post-header">
                    <div class="skeleton skeleton-avatar"></div>
                    <div class="skeleton-user-info">
                        <div class="skeleton skeleton-username"></div>
                        <div class="skeleton skeleton-time"></div>
                    </div>
                </div>
                <div class="skeleton skeleton-content"></div>
                <div class="skeleton skeleton-media"></div>
                <div class="skeleton-actions">
                    <div class="skeleton skeleton-action"></div>
                    <div class="skeleton skeleton-action"></div>
                </div>
            </div>
        `).join('');

        postsContainer.innerHTML = skeletonHTML;
    }

    function showPostsLoading() {
        if (!postsContainer) return;

        postsContainer.innerHTML = `
            <div class="posts-loading">
                <div class="spinner-large"></div>
                <p>Loading posts...</p>
            </div>
        `;
    }

    function showSkeletonActivity() {
        if (!activityList) return;

        const skeletonHTML = Array(5).fill(0).map(() => `
            <div class="skeleton-activity">
                <div class="skeleton skeleton-activity-text"></div>
                <div class="skeleton skeleton-activity-time"></div>
            </div>
        `).join('');

        activityList.innerHTML = skeletonHTML;
    }



    function showButtonLoading(button) {
        if (!button) return;
        button.classList.add('btn-loading');
        button.disabled = true;
    }

    function hideButtonLoading(button) {
        if (!button) return;
        button.classList.remove('btn-loading');
        button.disabled = false;
    }

    async function loadInitialPosts() {
        try {
            // Show loading indicator in posts container
            // Use showPostsLoading() for simple spinner or showSkeletonPosts() for skeleton loading
            showPostsLoading();

            // Add cache-busting parameter to ensure fresh data
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/posts?limit=5&_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const posts = await res.json();
                await renderPosts(posts);

                // Initialize infinite scroll after initial posts are loaded
                if (window.InfiniteScrollUtils && posts.length === 5) {
                    postsInfiniteScroll = window.InfiniteScrollUtils.createPostsScroll(
                        postsContainer,
                        (post) => createPostElement(post)
                    );
                }
            }
        } catch (err) {
            console.error('Error loading posts:', err);
            postsContainer.innerHTML = '<p class="no-posts">Failed to load posts. Please refresh the page.</p>';
        } finally {
            isInitialLoad = false;
        }
    }

    async function loadPosts() {
        try {
            // Show loading indicator when refreshing posts
            // Use showPostsLoading() for simple spinner or showSkeletonPosts() for skeleton loading
            showPostsLoading();

            // Add cache-busting parameter to ensure fresh data
            const timestamp = new Date().getTime();
            const res = await fetch(`/api/posts?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const posts = await res.json();
                await renderPosts(posts);
            }
        } catch (err) {
            console.error('Error loading posts:', err);
            postsContainer.innerHTML = '<p class="no-posts">Failed to load posts. Please refresh the page.</p>';
        }
    }

    async function renderPosts(posts) {
        postsContainer.innerHTML = '';

        if (posts.length === 0) {
            postsContainer.innerHTML = '<p class="no-posts">No posts yet. Be the first to post!</p>';
            return;
        }

        // Get current user's email and all user profiles
        let userEmail = '';
        let allUserProfiles = {};
        const timestamp = new Date().getTime();
        try {
            const res = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (res.ok) {
                const data = await res.json();
                userEmail = data.email;

                // Fetch all user profiles to get updated names for everyone
                const usersRes = await fetch(`/api/users?_t=${timestamp}`, {
                    credentials: 'include',
                    cache: 'no-cache'
                });
                if (usersRes.ok) {
                    const users = await usersRes.json();
                    // Create a map of email -> profile for quick lookup
                    users.forEach(user => {
                        allUserProfiles[user.email] = user;
                    });
                    console.log('All user profiles loaded:', Object.keys(allUserProfiles).length, 'users');
                }
            }
        } catch (err) {
            console.error('Error fetching user info:', err);
        }

        posts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        posts.forEach((post, index) => {
            const postElement = createPostElement(post, userEmail, allUserProfiles);

            // Add stagger animation for initial load
            if (isInitialLoad) {
                postElement.classList.add('stagger-item');
                postElement.style.animationDelay = `${index * 0.1}s`;
            } else {
                postElement.classList.add('fade-in');
            }

            postsContainer.appendChild(postElement);
        });

        lucide.createIcons();

        // Observe new lazy images
        if (window.LazyLoadingUtils) {
            window.LazyLoadingUtils.observeNewContent(postsContainer);
        }

        // Event listeners are now handled in addPostEventListeners function
        // called from createPostElement for each individual post


    }

    // Create individual post element (used by infinite scroll)
    function createPostElement(post, userEmail = '', allUserProfiles = {}) {
        const postElement = document.createElement('div');
        postElement.className = 'post card-hover';
        postElement.setAttribute('data-post-id', post.id);

        // Check if current user liked this post
        const isLiked = post.likedBy && post.likedBy.includes(userEmail);
        const likeClass = isLiked ? 'liked' : '';

        // Use updated username for ALL users' posts
        let displayName = post.userName || 'Anonymous';
        const userProfile = allUserProfiles[post.userEmail];
        if (userProfile) {
            const updatedName = `${userProfile.firstname} ${userProfile.lastname}`;
            displayName = updatedName;
        }

        let mediaContent = '';
        if (post.mediaUrl) {
            if (post.mediaType === 'image') {
                mediaContent = `<div class="post-media"><img data-src="${post.mediaUrl}" alt="Post image" class="lazy-loading"></div>`;
            } else if (post.mediaType === 'video') {
                mediaContent = `
                    <div class="post-media">
                        <video controls data-src="${post.mediaUrl}" class="lazy-loading">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                `;
            }
        }

        const commentCount = post.comments ? post.comments.length : 0;

        postElement.innerHTML = `
            <div class="post-header">
                <div class="post-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="post-user">${displayName}</span>
                    <span class="post-time">${formatDate(post.createdAt)}</span>
                </div>
            </div>
            <div class="post-content">${post.content}</div>
            ${mediaContent}
            <div class="post-actions">
                <button class="btn-like btn-animated ripple ${likeClass}" data-post-id="${post.id}">
                    <i data-lucide="thumbs-up"></i>
                    <span class="like-count">${post.likes || 0}</span>
                </button>
                <button class="btn-view-comments btn-animated ripple" data-post-id="${post.id}">
                    <i data-lucide="message-circle"></i>
                    <span class="comment-count">${commentCount}</span> Comments
                </button>
            </div>
            <div class="post-comments" id="comments-${post.id}" style="display: none;">
                <div class="comments-container" id="comments-container-${post.id}">
                    <!-- Comments will be loaded here -->
                </div>
                <div class="comment-form">
                    <textarea class="comment-input" placeholder="Write a comment..." data-post-id="${post.id}"></textarea>
                    <button class="btn-post-comment btn-animated" data-post-id="${post.id}">Post Comment</button>
                </div>
            </div>
        `;

        // Add event listeners
        addPostEventListeners(postElement);

        return postElement;
    }

    // Add event listeners to post element
    function addPostEventListeners(postElement) {
        const postId = postElement.getAttribute('data-post-id');

        // Like button
        const likeBtn = postElement.querySelector('.btn-like');
        if (likeBtn) {
            likeBtn.addEventListener('click', async function() {
                const likeCountElement = this.querySelector('.like-count');
                const iconElement = this.querySelector('i');
                const isLiked = this.classList.contains('liked');

                // Optimistic UI update
                this.classList.toggle('liked');
                const currentLikes = parseInt(likeCountElement.textContent);
                likeCountElement.textContent = isLiked ? currentLikes - 1 : currentLikes + 1;
                if (iconElement) {
                    iconElement.setAttribute('fill', isLiked ? 'none' : 'currentColor');
                }

                try {
                    const res = await fetch(`/api/posts/like?post_id=${postId}`, {
                        method: 'POST',
                        credentials: 'include'
                    });

                    if (!res.ok) {
                        // Revert UI if API call fails
                        this.classList.toggle('liked');
                        likeCountElement.textContent = isLiked ? currentLikes : currentLikes - 1;
                        if (iconElement) {
                            iconElement.setAttribute('fill', isLiked ? 'currentColor' : 'none');
                        }
                        throw new Error('Failed to like post');
                    } else {
                        // Refresh activity after successful like
                        if (activityList) {
                            loadRecentActivity();
                        }
                    }
                } catch (err) {
                    console.error('Error liking post:', err);
                }
            });
        }

        // View comments button
        const commentsBtn = postElement.querySelector('.btn-view-comments');
        if (commentsBtn) {
            commentsBtn.addEventListener('click', async function() {
                const commentsSection = postElement.querySelector(`#comments-${postId}`);

                if (commentsSection.style.display === 'none') {
                    commentsSection.style.display = 'block';
                    commentsSection.classList.add('slide-in-down');
                    await loadComments(postId);
                } else {
                    commentsSection.classList.add('slide-out');
                    setTimeout(() => {
                        commentsSection.style.display = 'none';
                        commentsSection.classList.remove('slide-out');
                    }, 300);
                }
            });
        }

        // Post comment button
        const postCommentBtn = postElement.querySelector('.btn-post-comment');
        if (postCommentBtn) {
            postCommentBtn.addEventListener('click', async function() {
                const commentInput = postElement.querySelector(`.comment-input[data-post-id="${postId}"]`);
                const content = commentInput.value.trim();

                if (!content) {
                    commentInput.classList.add('shake');
                    setTimeout(() => commentInput.classList.remove('shake'), 500);
                    return;
                }

                await postComment(postId, content, commentInput);
            });
        }

        // Comment input (Enter key)
        const commentInput = postElement.querySelector('.comment-input');
        if (commentInput) {
            commentInput.addEventListener('keypress', async function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const content = this.value.trim();

                    if (!content) return;

                    await postComment(postId, content, this);
                }
            });
        }
    }

    async function loadComments(postId) {
        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                credentials: 'include'
            });

            if (res.ok) {
                const comments = await res.json();
                await renderComments(postId, comments);
            }
        } catch (err) {
            console.error('Error loading comments:', err);
        }
    }

    async function renderComments(postId, comments) {
        const commentsContainer = document.getElementById(`comments-container-${postId}`);

        if (comments.length === 0) {
            commentsContainer.innerHTML = '<p class="no-comments">No comments yet. Be the first to comment!</p>';
            return;
        }

        // Get current user email and all user profiles
        let currentUserEmail = '';
        let allUserProfiles = {};
        try {
            // Get current user email
            const authRes = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (authRes.ok) {
                const authData = await authRes.json();
                currentUserEmail = authData.email;
            }

            // Get all user profiles to update comment names for everyone
            const timestamp = new Date().getTime();
            const usersRes = await fetch(`/api/users?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });
            if (usersRes.ok) {
                const users = await usersRes.json();
                users.forEach(user => {
                    allUserProfiles[user.email] = user;
                });
            }
        } catch (err) {
            console.error('Error fetching user profiles:', err);
        }

        // Sort comments by date (newest first, like Facebook)
        comments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        commentsContainer.innerHTML = comments.map(comment => {
            // Use updated username for ALL users' comments
            let displayName = comment.userName || 'Anonymous';
            const userProfile = allUserProfiles[comment.userEmail];
            if (userProfile) {
                displayName = `${userProfile.firstname} ${userProfile.lastname}`;
            }

            const likeCount = comment.likes || 0;
            const isLiked = comment.likedBy && comment.likedBy.includes(currentUserEmail);
            const likeClass = isLiked ? 'liked' : '';

            // Render replies
            const repliesHtml = comment.replies && comment.replies.length > 0 ?
                comment.replies.map(reply => {
                    let replyDisplayName = reply.userName || 'Anonymous';
                    const replyUserProfile = allUserProfiles[reply.userEmail];
                    if (replyUserProfile) {
                        replyDisplayName = `${replyUserProfile.firstname} ${replyUserProfile.lastname}`;
                    }

                    const replyLikeCount = reply.likes || 0;
                    const isReplyLiked = reply.likedBy && reply.likedBy.includes(currentUserEmail);
                    const replyLikeClass = isReplyLiked ? 'liked' : '';

                    return `
                        <div class="reply" data-reply-id="${reply.id}">
                            <div class="reply-header">
                                <div class="reply-avatar"><i data-lucide="user"></i></div>
                                <div>
                                    <span class="reply-user">${replyDisplayName}</span>
                                    <span class="reply-time">${formatDate(reply.createdAt)}</span>
                                </div>
                            </div>
                            <div class="reply-content">${reply.content}</div>
                            <div class="reply-actions">
                                <button class="btn-like-reply ${replyLikeClass}" data-post-id="${postId}" data-comment-id="${comment.id}" data-reply-id="${reply.id}">
                                    <i data-lucide="thumbs-up"></i>
                                    <span class="reply-like-count">${replyLikeCount}</span>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('') : '';

            // Create hide/show replies button if there are replies
            const replyToggleButton = comment.replies && comment.replies.length > 0 ?
                `<button class="btn-toggle-replies" data-comment-id="${comment.id}">
                    <i data-lucide="chevron-down"></i>
                    Hide ${comment.replies.length} ${comment.replies.length === 1 ? 'reply' : 'replies'}
                </button>` : '';

            return `
                <div class="comment" data-comment-id="${comment.id}">
                    <div class="comment-header">
                        <div class="comment-avatar"><i data-lucide="user"></i></div>
                        <div>
                            <span class="comment-user">${displayName}</span>
                            <span class="comment-time">${formatDate(comment.createdAt)}</span>
                        </div>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                    <div class="comment-actions">
                        <button class="btn-like-comment ${likeClass}" data-post-id="${postId}" data-comment-id="${comment.id}">
                            <i data-lucide="thumbs-up"></i>
                            <span class="comment-like-count">${likeCount}</span>
                        </button>
                        <button class="btn-reply-comment" data-post-id="${postId}" data-comment-id="${comment.id}">
                            <i data-lucide="message-circle"></i>
                            Reply
                        </button>
                    </div>
                    ${comment.replies && comment.replies.length > 0 ? `
                        <div class="replies-section">
                            ${replyToggleButton}
                            <div class="comment-replies" id="replies-${comment.id}">${repliesHtml}</div>
                        </div>
                    ` : ''}
                    <div class="reply-form" id="reply-form-${comment.id}">
                        <textarea class="reply-input" placeholder="Write a reply..." data-comment-id="${comment.id}"></textarea>
                        <button class="btn-post-reply" data-post-id="${postId}" data-comment-id="${comment.id}">Post Reply</button>
                        <button class="btn-cancel-reply" data-comment-id="${comment.id}">Cancel</button>
                    </div>
                </div>
            `;
        }).join('');

        lucide.createIcons();
    }

    async function postComment(postId, content, inputElement) {
        // Get current user info for optimistic update
        let currentUser = '';
        let currentUserName = 'You';
        try {
            const res = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (res.ok) {
                const data = await res.json();
                currentUser = data.email;

                // Get current user's profile for updated name
                const timestamp = new Date().getTime();
                const profileRes = await fetch(`/api/profile?_t=${timestamp}`, {
                    credentials: 'include',
                    cache: 'no-cache'
                });
                if (profileRes.ok) {
                    const profileData = await profileRes.json();
                    currentUserName = `${profileData.user.firstname} ${profileData.user.lastname}`;
                }
            }
        } catch (err) {
            console.error('Error fetching user info:', err);
        }

        // Create temporary comment for optimistic UI update
        const tempComment = {
            id: 'temp-' + Date.now(),
            content: content,
            userName: currentUserName,
            createdAt: new Date().toISOString(),
            userEmail: currentUser
        };

        // Add comment to UI immediately
        const commentsContainer = document.getElementById(`comments-container-${postId}`);
        const noCommentsMsg = commentsContainer.querySelector('.no-comments');
        if (noCommentsMsg) {
            noCommentsMsg.remove();
        }

        const commentElement = document.createElement('div');
        commentElement.className = 'comment';
        commentElement.setAttribute('data-comment-id', tempComment.id);
        commentElement.innerHTML = `
            <div class="comment-header">
                <div class="comment-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="comment-user">${tempComment.userName}</span>
                    <span class="comment-time">${formatDate(tempComment.createdAt)}</span>
                </div>
            </div>
            <div class="comment-content">${tempComment.content}</div>
            <div class="comment-actions">
                <button class="btn-like-comment" data-post-id="${postId}" data-comment-id="${tempComment.id}">
                    <i data-lucide="thumbs-up"></i>
                    <span class="comment-like-count">0</span>
                </button>
                <button class="btn-reply-comment" data-post-id="${postId}" data-comment-id="${tempComment.id}">
                    <i data-lucide="message-circle"></i>
                    Reply
                </button>
            </div>
            <div class="reply-form" id="reply-form-${tempComment.id}" style="display: none;">
                <textarea class="reply-input" placeholder="Write a reply..." data-comment-id="${tempComment.id}"></textarea>
                <button class="btn-post-reply" data-post-id="${postId}" data-comment-id="${tempComment.id}">Post Reply</button>
                <button class="btn-cancel-reply" data-comment-id="${tempComment.id}">Cancel</button>
            </div>
        `;
        // Add new comment at the top (newest first)
        commentsContainer.insertBefore(commentElement, commentsContainer.firstChild);
        lucide.createIcons();

        // Update comment count
        const commentCountElement = document.querySelector(`.btn-view-comments[data-post-id="${postId}"] .comment-count`);
        if (commentCountElement) {
            const currentCount = parseInt(commentCountElement.textContent);
            commentCountElement.textContent = currentCount + 1;
        }

        // Clear input
        inputElement.value = '';

        try {
            const res = await fetch(`/api/posts/${postId}/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ content: content })
            });

            if (res.ok) {
                const newComment = await res.json();
                // Replace temporary comment with real comment
                commentElement.setAttribute('data-comment-id', newComment.id);
                commentElement.querySelector('.comment-user').textContent = newComment.userName;
                commentElement.querySelector('.comment-time').textContent = formatDate(newComment.createdAt);

                // Refresh activity after successful comment
                if (activityList) {
                    loadRecentActivity();
                }
            } else {
                throw new Error('Failed to post comment');
            }
        } catch (err) {
            console.error('Error posting comment:', err);
            // Remove the temporary comment on error
            commentElement.remove();

            // Revert comment count
            if (commentCountElement) {
                const currentCount = parseInt(commentCountElement.textContent);
                commentCountElement.textContent = currentCount - 1;
            }

            // Restore input value
            inputElement.value = content;
            alert('Failed to post comment. Please try again.');
        }
    }

    async function likeComment(postId, commentId, buttonElement) {
        // Immediate UI update (optimistic)
        const likeCountElement = buttonElement.querySelector('.comment-like-count');
        const currentCount = parseInt(likeCountElement.textContent) || 0;
        const wasLiked = buttonElement.classList.contains('liked');

        // Update UI immediately
        if (wasLiked) {
            buttonElement.classList.remove('liked');
            likeCountElement.textContent = Math.max(0, currentCount - 1);
        } else {
            buttonElement.classList.add('liked');
            likeCountElement.textContent = currentCount + 1;
        }

        try {
            const res = await fetch(`/api/posts/${postId}/comments/${commentId}/like`, {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                const data = await res.json();

                // Verify the UI matches the server response
                if ((data.status === 'liked') !== buttonElement.classList.contains('liked')) {
                    // Fix any mismatch
                    if (data.status === 'liked') {
                        buttonElement.classList.add('liked');
                        likeCountElement.textContent = currentCount + 1;
                    } else {
                        buttonElement.classList.remove('liked');
                        likeCountElement.textContent = Math.max(0, currentCount - 1);
                    }
                }
            } else {
                // Revert UI changes on error
                if (wasLiked) {
                    buttonElement.classList.add('liked');
                    likeCountElement.textContent = currentCount;
                } else {
                    buttonElement.classList.remove('liked');
                    likeCountElement.textContent = currentCount;
                }
                const errorText = await res.text();
                console.error('Like comment failed:', res.status, errorText);
            }
        } catch (err) {
            // Revert UI changes on error
            if (wasLiked) {
                buttonElement.classList.add('liked');
                likeCountElement.textContent = currentCount;
            } else {
                buttonElement.classList.remove('liked');
                likeCountElement.textContent = currentCount;
            }
            console.error('Error liking comment:', err);
        }
    }

    async function postReply(postId, commentId, content, inputElement) {
        // Get current user info for optimistic update
        let currentUser = '';
        let currentUserName = 'You';
        try {
            const res = await fetch('/api/check-auth', {
                credentials: 'include'
            });
            if (res.ok) {
                const data = await res.json();
                currentUser = data.email;

                // Get current user's profile for updated name
                const timestamp = new Date().getTime();
                const profileRes = await fetch(`/api/profile?_t=${timestamp}`, {
                    credentials: 'include',
                    cache: 'no-cache'
                });
                if (profileRes.ok) {
                    const profileData = await profileRes.json();
                    currentUserName = `${profileData.user.firstname} ${profileData.user.lastname}`;
                }
            }
        } catch (err) {
            console.error('Error fetching user info:', err);
        }

        // Create temporary reply for optimistic UI update
        const tempReply = {
            id: 'temp-' + Date.now(),
            content: content,
            userName: currentUserName,
            createdAt: new Date().toISOString(),
            userEmail: currentUser,
            likes: 0,
            likedBy: []
        };

        // Add reply to UI immediately
        const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
        let repliesSection = commentElement.querySelector('.replies-section');
        let repliesContainer = commentElement.querySelector('.comment-replies');

        if (!repliesSection) {
            // Create new replies section
            repliesSection = document.createElement('div');
            repliesSection.className = 'replies-section';

            // Create toggle button
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn-toggle-replies';
            toggleButton.setAttribute('data-comment-id', commentId);
            toggleButton.innerHTML = `<i data-lucide="chevron-down"></i>Hide 1 reply`;

            // Create replies container
            repliesContainer = document.createElement('div');
            repliesContainer.className = 'comment-replies';
            repliesContainer.id = `replies-${commentId}`;

            repliesSection.appendChild(toggleButton);
            repliesSection.appendChild(repliesContainer);

            const replyForm = commentElement.querySelector('.reply-form');
            commentElement.insertBefore(repliesSection, replyForm);

            // Add event listener to toggle button
            toggleButton.addEventListener('click', function() {
                const commentId = this.getAttribute('data-comment-id');
                const repliesContainer = document.getElementById(`replies-${commentId}`);

                if (repliesContainer.style.display === 'none') {
                    // Show replies
                    repliesContainer.style.display = 'block';
                    this.classList.remove('collapsed');
                    const replyCount = repliesContainer.querySelectorAll('.reply').length;
                    this.innerHTML = `<i data-lucide="chevron-down"></i>Hide ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
                } else {
                    // Hide replies
                    repliesContainer.style.display = 'none';
                    this.classList.add('collapsed');
                    const replyCount = repliesContainer.querySelectorAll('.reply').length;
                    this.innerHTML = `<i data-lucide="chevron-right"></i>Show ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
                }
                lucide.createIcons();
            });
        } else {
            // Update existing toggle button text
            const toggleButton = repliesSection.querySelector('.btn-toggle-replies');
            const currentReplyCount = repliesContainer.querySelectorAll('.reply').length + 1;
            if (!toggleButton.classList.contains('collapsed')) {
                toggleButton.innerHTML = `<i data-lucide="chevron-down"></i>Hide ${currentReplyCount} ${currentReplyCount === 1 ? 'reply' : 'replies'}`;
            } else {
                toggleButton.innerHTML = `<i data-lucide="chevron-right"></i>Show ${currentReplyCount} ${currentReplyCount === 1 ? 'reply' : 'replies'}`;
            }
        }

        const replyElement = document.createElement('div');
        replyElement.className = 'reply';
        replyElement.setAttribute('data-reply-id', tempReply.id);
        replyElement.innerHTML = `
            <div class="reply-header">
                <div class="reply-avatar"><i data-lucide="user"></i></div>
                <div>
                    <span class="reply-user">${tempReply.userName}</span>
                    <span class="reply-time">${formatDate(tempReply.createdAt)}</span>
                </div>
            </div>
            <div class="reply-content">${tempReply.content}</div>
            <div class="reply-actions">
                <button class="btn-like-reply" data-post-id="${postId}" data-comment-id="${commentId}" data-reply-id="${tempReply.id}">
                    <i data-lucide="thumbs-up"></i>
                    <span class="reply-like-count">0</span>
                </button>
            </div>
        `;

        repliesContainer.appendChild(replyElement);
        lucide.createIcons();

        // Add event listener to the new reply like button
        const newLikeButton = replyElement.querySelector('.btn-like-reply');
        newLikeButton.addEventListener('click', async function() {
            const postId = this.getAttribute('data-post-id');
            const commentId = this.getAttribute('data-comment-id');
            const replyId = this.getAttribute('data-reply-id');
            await likeReply(postId, commentId, replyId, this);
        });

        // Clear input and hide form
        inputElement.value = '';
        const replyForm = document.getElementById(`reply-form-${commentId}`);
        replyForm.style.display = 'none';

        try {
            const res = await fetch(`/api/posts/${postId}/comments/${commentId}/replies`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ content: content })
            });

            if (res.ok) {
                const newReply = await res.json();
                // Replace temporary reply with real reply
                replyElement.setAttribute('data-reply-id', newReply.id);
                replyElement.querySelector('.reply-user').textContent = newReply.userName;
                replyElement.querySelector('.reply-time').textContent = formatDate(newReply.createdAt);

                // Update the like button data attributes
                newLikeButton.setAttribute('data-reply-id', newReply.id);

                // Refresh activity after successful reply
                if (activityList) {
                    loadRecentActivity();
                }
            } else {
                throw new Error('Failed to post reply');
            }
        } catch (err) {
            console.error('Error posting reply:', err);
            // Remove the temporary reply on error
            replyElement.remove();

            // Restore input value
            inputElement.value = content;
            replyForm.style.display = 'block';
            alert('Failed to post reply. Please try again.');
        }
    }

    async function likeReply(postId, commentId, replyId, buttonElement) {
        // Immediate UI update (optimistic)
        const likeCountElement = buttonElement.querySelector('.reply-like-count');
        const currentCount = parseInt(likeCountElement.textContent) || 0;
        const wasLiked = buttonElement.classList.contains('liked');

        // Update UI immediately
        if (wasLiked) {
            buttonElement.classList.remove('liked');
            likeCountElement.textContent = Math.max(0, currentCount - 1);
        } else {
            buttonElement.classList.add('liked');
            likeCountElement.textContent = currentCount + 1;
        }

        try {
            const res = await fetch(`/api/posts/${postId}/comments/${commentId}/replies/${replyId}/like`, {
                method: 'POST',
                credentials: 'include'
            });

            if (res.ok) {
                const data = await res.json();

                // Verify the UI matches the server response
                if ((data.status === 'liked') !== buttonElement.classList.contains('liked')) {
                    // Fix any mismatch
                    if (data.status === 'liked') {
                        buttonElement.classList.add('liked');
                        likeCountElement.textContent = currentCount + 1;
                    } else {
                        buttonElement.classList.remove('liked');
                        likeCountElement.textContent = Math.max(0, currentCount - 1);
                    }
                }
            } else {
                // Revert UI changes on error
                if (wasLiked) {
                    buttonElement.classList.add('liked');
                    likeCountElement.textContent = currentCount;
                } else {
                    buttonElement.classList.remove('liked');
                    likeCountElement.textContent = currentCount;
                }
            }
        } catch (err) {
            // Revert UI changes on error
            if (wasLiked) {
                buttonElement.classList.add('liked');
                likeCountElement.textContent = currentCount;
            } else {
                buttonElement.classList.remove('liked');
                likeCountElement.textContent = currentCount;
            }
            console.error('Error liking reply:', err);
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = Math.floor((now - date) / 1000);
        if (diff < 60) return 'just now';
        if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
        return `${Math.floor(diff / 86400)} days ago`;
    }

    // Load recent activity for sidebar
    async function loadRecentActivity() {
        try {
            // Show skeleton loading state
            showSkeletonActivity();

            const timestamp = new Date().getTime();
            const res = await fetch(`/api/activity?_t=${timestamp}`, {
                credentials: 'include',
                cache: 'no-cache'
            });

            if (res.ok) {
                const activities = await res.json();
                renderRecentActivity(activities);
            } else {
                activityList.innerHTML = '<div class="no-activity">Unable to load recent activity</div>';
            }
        } catch (err) {
            console.error('Error loading recent activity:', err);
            activityList.innerHTML = '<div class="no-activity">Unable to load recent activity</div>';
        }
    }

    async function renderRecentActivity(activities) {
        if (!activities || activities.length === 0) {
            activityList.innerHTML = '<div class="no-activity">No recent activity in the last 24 hours</div>';
            return;
        }

        // Get current user's email to personalize activity display (with caching)
        let currentUserEmail = currentUserEmailCache;
        if (!currentUserEmail) {
            try {
                const res = await fetch('/api/check-auth', {
                    credentials: 'include'
                });
                if (res.ok) {
                    const data = await res.json();
                    currentUserEmail = data.email;
                    currentUserEmailCache = currentUserEmail; // Cache for future use
                }
            } catch (err) {
                console.error('Error fetching current user:', err);
            }
        }

        activityList.innerHTML = activities.map(activity => {
            let icon = '';
            let actionText = '';
            let contentText = activity.content;

            // Check if this is the current user's activity
            const isCurrentUser = activity.userEmail === currentUserEmail;
            const displayName = isCurrentUser ? 'You' : (activity.userName || 'Unknown User');

            // Get post owner name for contextual messages
            const postOwnerName = activity.postOwnerName || 'Unknown User';

            switch (activity.type) {
                case 'post':
                    icon = '<i data-lucide="edit-3"></i>';
                    actionText = 'posted';
                    break;
                case 'like':
                    icon = '<i data-lucide="thumbs-up"></i>';
                    // Determine whose post was liked
                    if (activity.postOwner === currentUserEmail) {
                        actionText = isCurrentUser ? 'liked your own post' : 'liked your post';
                    } else if (isCurrentUser) {
                        actionText = `liked ${postOwnerName}'s post`;
                    } else {
                        actionText = `liked ${postOwnerName}'s post`;
                    }
                    contentText = '';
                    break;
                case 'comment':
                    icon = '<i data-lucide="message-circle"></i>';
                    // Determine whose post was commented on
                    if (activity.postOwner === currentUserEmail) {
                        actionText = isCurrentUser ? 'commented on your own post' : 'commented on your post';
                    } else if (isCurrentUser) {
                        actionText = `commented on ${postOwnerName}'s post`;
                    } else {
                        actionText = `commented on ${postOwnerName}'s post`;
                    }
                    // For comments, extract the actual comment text
                    if (activity.content && activity.content.startsWith('commented: ')) {
                        contentText = activity.content.substring(11); // Remove "commented: " prefix
                    } else {
                        contentText = activity.content || '';
                    }
                    break;
                case 'comment_like':
                    icon = '<i data-lucide="thumbs-up"></i>';
                    actionText = isCurrentUser ? 'liked your own comment' : 'liked your comment';
                    contentText = activity.content || '';
                    break;
                case 'comment_reply':
                    icon = '<i data-lucide="corner-down-right"></i>';
                    actionText = isCurrentUser ? 'replied to your own comment' : 'replied to your comment';
                    contentText = activity.content || '';
                    break;
                case 'reply_like':
                    icon = '<i data-lucide="thumbs-up"></i>';
                    actionText = isCurrentUser ? 'liked your own reply' : 'liked your reply';
                    contentText = activity.content || '';
                    break;
            }

            return `
                <div class="activity-item" data-activity-type="${activity.type}" data-is-current-user="${isCurrentUser}" data-post-id="${activity.postId || ''}" onclick="navigateToPost('${activity.postId || ''}')">
                    <div class="activity-icon">${icon}</div>
                    <div class="activity-content">
                        <div class="activity-text">
                            <strong>${displayName}</strong> ${actionText}
                            ${contentText ? `<div class="activity-preview">${contentText}</div>` : ''}
                        </div>
                        <div class="activity-time">${formatDate(activity.createdAt)}</div>
                    </div>
                </div>
            `;
        }).join('');

        // Re-create icons for the activity items
        lucide.createIcons();
    }

    // Navigate to a specific post (scroll to it if on home page)
    window.navigateToPost = function(postId) {
        if (!postId) return;

        // If we're on the home page, try to scroll to the post
        const postElement = document.querySelector(`[data-post-id="${postId}"]`);
        if (postElement) {
            postElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // Add a highlight effect
            postElement.style.backgroundColor = '#f0f8ff';
            setTimeout(() => {
                postElement.style.backgroundColor = '';
            }, 2000);
        }
    };

    // Global function to refresh activity (can be called from other scripts)
    window.refreshActivity = function() {
        if (activityList) {
            loadRecentActivity();
        }
    };
});

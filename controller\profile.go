package controller

import (
	"context"
	"net/http"
	"prj/model"
	"prj/utils/httpResp"
	"sort"
	"time"

	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetUserProfile(w http.ResponseWriter, r *http.Request) {
	email := r.Context().Value("user_email").(string)

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "user not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch user")
		return
	}

	// Get user's posts
	cursor, err := model.PostCollection.Find(context.TODO(), bson.M{"userEmail": email})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch posts")
		return
	}
	defer cursor.Close(context.TODO())

	var posts []model.Post
	if err = cursor.All(context.TODO(), &posts); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to decode posts")
		return
	}

	// Ensure LikedBy is never nil for frontend
	for i := range posts {
		if posts[i].LikedBy == nil {
			posts[i].LikedBy = []string{}
		}
	}

	response := map[string]interface{}{
		"user": map[string]string{
			"firstname": user.FirstName,
			"lastname":  user.LastName,
			"email":     user.Email,
		},
		"posts": posts,
	}

	httpResp.RespondWithJSON(w, http.StatusOK, response)
}

// Add these functions to profile.go
func GetAllUsers(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := model.UserCollection.Find(ctx, bson.M{})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch users")
		return
	}
	defer cursor.Close(ctx)

	var users []model.User
	if err = cursor.All(ctx, &users); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to decode users")
		return
	}

	// Remove sensitive data before sending
	var sanitizedUsers []map[string]string
	for _, user := range users {
		sanitizedUsers = append(sanitizedUsers, map[string]string{
			"firstname": user.FirstName,
			"lastname":  user.LastName,
			"email":     user.Email,
		})
	}

	httpResp.RespondWithJSON(w, http.StatusOK, sanitizedUsers)
}

func GetUserPosts(w http.ResponseWriter, r *http.Request) {
	email := mux.Vars(r)["email"]

	// Get user details
	var user model.User
	err := model.UserCollection.FindOne(context.TODO(), bson.M{"email": email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			httpResp.RespondWithError(w, http.StatusNotFound, "user not found")
			return
		}
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch user")
		return
	}

	// Get user's posts (only from PostCollection)
	cursor, err := model.PostCollection.Find(context.TODO(), bson.M{"userEmail": email})
	if err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to fetch posts")
		return
	}
	defer cursor.Close(context.TODO())

	var posts []model.Post
	if err = cursor.All(context.TODO(), &posts); err != nil {
		httpResp.RespondWithError(w, http.StatusInternalServerError, "failed to decode posts")
		return
	}

	// Ensure LikedBy is never nil for frontend
	for i := range posts {
		if posts[i].LikedBy == nil {
			posts[i].LikedBy = []string{}
		}
	}

	// Sort posts by date (newest first)
	sort.Slice(posts, func(i, j int) bool {
		return posts[i].CreatedAt.After(posts[j].CreatedAt)
	})

	response := map[string]interface{}{
		"user": map[string]string{
			"firstname": user.FirstName,
			"lastname":  user.LastName,
			"email":     user.Email,
		},
		"posts": posts,
	}

	httpResp.RespondWithJSON(w, http.StatusOK, response)
}

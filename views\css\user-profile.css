/* user-profile.css */
.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
}

.profile-avatar i {
    width: 40px;
    height: 40px;
    color: #666;
}

.profile-info h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.profile-info p {
    color: #666;
    font-size: 1rem;
    margin: 0.2rem 0;
}

.profile-content h2 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.posts-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.no-posts {
    text-align: center;
    color: #666;
    padding: 2rem;
    font-style: italic;
}

/* Post styles */
.post {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.post-avatar i {
    width: 20px;
    height: 20px;
    color: #666;
}

.post-user {
    font-weight: 600;
    color: #333;
    display: block;
}

.post-time {
    font-size: 0.8rem;
    color: #666;
}

.post-content {
    margin-bottom: 1rem;
    line-height: 1.5;
    color: #333;
}

.post-media {
    margin-bottom: 1rem;
}

.post-media img,
.post-media video {
    width: 100%;
    max-width: 100%;
    border-radius: 8px;
}

.post-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.btn-like,
.btn-view-comments {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
}

.btn-like:hover,
.btn-view-comments:hover {
    background: #f0f0f0;
    color: #333;
}

.btn-like.liked {
    color: #007bff;
}

.btn-like.liked i {
    fill: currentColor;
}

/* Comments section - matching general.css exactly */
.post-comments {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.comment {
    padding: 0.75rem;
    background: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-avatar {
    width: 30px;
    height: 30px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.comment-avatar i {
    width: 16px;
    height: 16px;
    color: #666;
}

.comment-user {
    font-weight: 600;
    font-size: 0.9rem;
    display: block;
}

.comment-time {
    font-size: 0.7rem;
    color: #666;
}

.comment-content {
    font-size: 0.9rem;
    margin-left: 2.25rem;
}

.comment-form {
    margin-top: 1rem;
}

.comment-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    resize: vertical;
    min-height: 60px;
    box-sizing: border-box;
}

.btn-post-comment {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-post-comment:hover {
    background: #3a5bd9;
}

.no-comments {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .profile-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .profile-container {
        padding: 0 0.5rem;
    }

    .post {
        padding: 1rem;
    }

    .post-actions {
        flex-wrap: wrap;
    }

    .comment-form {
        flex-direction: column;
    }
}
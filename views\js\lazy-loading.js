// Lazy Loading Implementation
class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                // Load images when they're 100px away from viewport
                rootMargin: '100px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            this.observeImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src], video[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(element) {
        const src = element.getAttribute('data-src');
        if (!src) return;

        // Add loading class
        element.classList.add('lazy-loading');

        if (element.tagName === 'IMG') {
            // For images
            const img = new Image();
            img.onload = () => {
                element.src = src;
                element.classList.remove('lazy-loading');
                element.classList.add('fade-in');
                element.removeAttribute('data-src');
            };
            img.onerror = () => {
                element.classList.remove('lazy-loading');
                element.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
                element.removeAttribute('data-src');
            };
            img.src = src;
        } else if (element.tagName === 'VIDEO') {
            // For videos
            element.src = src;
            element.classList.remove('lazy-loading');
            element.classList.add('fade-in');
            element.removeAttribute('data-src');
        }
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll('img[data-src], video[data-src]');
        lazyImages.forEach(element => {
            this.loadImage(element);
        });
    }

    // Method to add new images to observation
    observeNewImages(container = document) {
        if (!this.imageObserver) return;
        
        const newLazyImages = container.querySelectorAll('img[data-src], video[data-src]');
        newLazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    // Method to create lazy image element
    createLazyImage(src, alt = '', className = '') {
        const img = document.createElement('img');
        img.setAttribute('data-src', src);
        img.alt = alt;
        img.className = className;
        
        // Add placeholder
        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+';
        
        if (this.imageObserver) {
            this.imageObserver.observe(img);
        } else {
            this.loadImage(img);
        }
        
        return img;
    }

    // Method to create lazy video element
    createLazyVideo(src, className = '') {
        const video = document.createElement('video');
        video.setAttribute('data-src', src);
        video.className = className;
        video.controls = true;
        video.preload = 'none';
        
        if (this.imageObserver) {
            this.imageObserver.observe(video);
        } else {
            this.loadImage(video);
        }
        
        return video;
    }
}

// Progressive Image Loading
class ProgressiveImageLoader {
    constructor() {
        this.init();
    }

    init() {
        this.loadProgressiveImages();
    }

    loadProgressiveImages() {
        const progressiveImages = document.querySelectorAll('img[data-src-low]');
        progressiveImages.forEach(img => {
            this.loadProgressiveImage(img);
        });
    }

    loadProgressiveImage(img) {
        const lowSrc = img.getAttribute('data-src-low');
        const highSrc = img.getAttribute('data-src');

        if (lowSrc) {
            // Load low quality image first
            img.src = lowSrc;
            img.classList.add('progressive-loading');
        }

        if (highSrc) {
            // Load high quality image
            const highImg = new Image();
            highImg.onload = () => {
                img.src = highSrc;
                img.classList.remove('progressive-loading');
                img.classList.add('progressive-loaded');
                img.removeAttribute('data-src');
                img.removeAttribute('data-src-low');
            };
            highImg.src = highSrc;
        }
    }
}

// Background Image Lazy Loading
class BackgroundLazyLoader {
    constructor() {
        this.observer = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadBackgroundImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeBackgroundImages();
        }
    }

    observeBackgroundImages() {
        const lazyBackgrounds = document.querySelectorAll('[data-bg-src]');
        lazyBackgrounds.forEach(element => {
            this.observer.observe(element);
        });
    }

    loadBackgroundImage(element) {
        const bgSrc = element.getAttribute('data-bg-src');
        if (!bgSrc) return;

        const img = new Image();
        img.onload = () => {
            element.style.backgroundImage = `url(${bgSrc})`;
            element.classList.add('bg-loaded');
            element.removeAttribute('data-bg-src');
        };
        img.src = bgSrc;
    }

    observeNewBackgrounds(container = document) {
        if (!this.observer) return;
        
        const newLazyBackgrounds = container.querySelectorAll('[data-bg-src]');
        newLazyBackgrounds.forEach(element => {
            this.observer.observe(element);
        });
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyLoader();
    window.progressiveLoader = new ProgressiveImageLoader();
    window.backgroundLazyLoader = new BackgroundLazyLoader();
});

// Utility functions for other scripts
window.LazyLoadingUtils = {
    // Function to convert regular img to lazy img
    makeLazy: function(img, src) {
        img.setAttribute('data-src', src);
        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+';
        if (window.lazyLoader) {
            window.lazyLoader.observeNewImages();
        }
    },

    // Function to observe new content
    observeNewContent: function(container) {
        if (window.lazyLoader) {
            window.lazyLoader.observeNewImages(container);
        }
        if (window.backgroundLazyLoader) {
            window.backgroundLazyLoader.observeNewBackgrounds(container);
        }
    }
};
